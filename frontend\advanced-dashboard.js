// advanced-dashboard.js
// Enhanced dashboard with real-time features and performance monitoring

const API_URL = '../backend/get_latest_data.php';

// Performance tracking
let performanceMetrics = {
    lastUpdateTime: null,
    updateCount: 0,
    averageResponseTime: 0,
    connectionStatus: 'connecting',
    dataFreshness: 'fresh'
};

// Enhanced data fetching with performance monitoring
async function fetchLatestData() {
    const startTime = performance.now();

    try {
        const response = await fetch(API_URL, {
            method: 'GET',
            headers: {
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();
        const endTime = performance.now();
        const responseTime = endTime - startTime;

        // Update performance metrics
        updatePerformanceMetrics(responseTime, 'online');
        updateDataFreshnessIndicators('fresh');

        return data;
    } catch (error) {
        console.error('Data fetch error:', error);
        updatePerformanceMetrics(0, 'error');
        updateDataFreshnessIndicators('error');
        return null;
    }
}

function updatePerformanceMetrics(responseTime, status) {
    performanceMetrics.updateCount++;
    performanceMetrics.lastUpdateTime = new Date();
    performanceMetrics.connectionStatus = status;

    if (responseTime > 0) {
        performanceMetrics.averageResponseTime =
            (performanceMetrics.averageResponseTime * (performanceMetrics.updateCount - 1) + responseTime) /
            performanceMetrics.updateCount;
    }

    // Update status indicators
    updateConnectionStatus(status);
    updatePerformanceDisplay();
}

function updateConnectionStatus(status) {
    const statusIndicators = document.querySelectorAll('.status-indicator');
    const systemStatus = document.querySelector('.system-status span:last-child');

    statusIndicators.forEach(indicator => {
        indicator.className = `status-indicator ${status}`;
    });

    if (systemStatus) {
        const statusText = {
            'online': 'System Online',
            'connecting': 'Connecting...',
            'error': 'Connection Error',
            'offline': 'System Offline'
        };
        systemStatus.textContent = statusText[status] || 'Unknown Status';
    }
}

function updateDataFreshnessIndicators(freshness) {
    const freshnessIndicators = document.querySelectorAll('.data-freshness');
    freshnessIndicators.forEach(indicator => {
        indicator.className = `data-freshness ${freshness}`;
        const statusText = indicator.querySelector('span:last-child');
        if (statusText) {
            const freshnessText = {
                'fresh': 'Live',
                'stale': 'Delayed',
                'error': 'Error'
            };
            statusText.textContent = freshnessText[freshness] || 'Unknown';
        }
    });
}

function updatePerformanceDisplay() {
    // Update last updated time
    const lastUpdatedElement = document.getElementById('lastUpdatedTime');
    if (lastUpdatedElement && performanceMetrics.lastUpdateTime) {
        lastUpdatedElement.textContent = performanceMetrics.lastUpdateTime.toLocaleTimeString();
    }

    // Update performance metrics in the UI
    const responseTimeElement = document.getElementById('responseTime');
    const updateCountElement = document.getElementById('updateCount');

    if (responseTimeElement) {
        responseTimeElement.textContent = `${Math.round(performanceMetrics.averageResponseTime)} ms`;
    }

    if (updateCountElement) {
        updateCountElement.textContent = performanceMetrics.updateCount.toString();
    }

    // Update performance metrics in console for debugging
    console.log('Performance Metrics:', {
        updates: performanceMetrics.updateCount,
        avgResponseTime: Math.round(performanceMetrics.averageResponseTime),
        status: performanceMetrics.connectionStatus
    });
}

// --- Track previous values for trend indicators ---
let prevAvgVoltage = null, prevTotalCurrent = null, prevTotalPower = null, prevFrequency = null;

// Update summary cards with trend
function updateSummaryCards(data) {
    if (!data) return;
    // Calculate averages
    const avgVoltage = data.voltage_1 && data.voltage_2 && data.voltage_3 ? ((data.voltage_1 + data.voltage_2 + data.voltage_3) / 3) : null;
    const totalCurrent = data.current_1 && data.current_2 && data.current_3 ? (data.current_1 + data.current_2 + data.current_3) : null;
    const totalPower = data.total_kw || null;
    const frequency = data.frequency || null;

    // Set values (remove summary cards)
    // document.getElementById('avgVoltage').textContent = avgVoltage !== null ? avgVoltage.toFixed(2) + ' V' : '-- V';
    // document.getElementById('totalCurrent').textContent = totalCurrent !== null ? totalCurrent.toFixed(2) + ' A' : '-- A';
    // document.getElementById('totalPower').textContent = totalPower !== null ? totalPower + ' kW' : '-- kW';
    // document.getElementById('freqValue').textContent = frequency !== null ? frequency + ' Hz' : '-- Hz';
    // document.getElementById('lastUpdatedTime').textContent = data.timestamp || 'N/A';

    // Calculate and update trends
    function setTrend(id, prev, current, decimals = 2) {
        const trendDiv = document.querySelector(`#${id} ~ .trend-indicator`);
        if (!trendDiv) return;

        if (prev === null || current === null) {
            trendDiv.className = 'trend-indicator stable';
            return;
        }

        const diff = current - prev;
        const threshold = Math.abs(current) * 0.01; // 1% threshold

        if (Math.abs(diff) < threshold) {
            trendDiv.className = 'trend-indicator stable';
        } else if (diff > 0) {
            trendDiv.className = 'trend-indicator rising';
        } else {
            trendDiv.className = 'trend-indicator falling';
        }

        trendDiv.textContent = `${diff > 0 ? '+' : ''}${diff.toFixed(decimals)}`;
    }

    // Update trends (commented out for now)
    // setTrend('avgVoltage', prevAvgVoltage, avgVoltage);
    // setTrend('totalCurrent', prevTotalCurrent, totalCurrent);
    // setTrend('totalPower', prevTotalPower, totalPower);
    // setTrend('freqValue', prevFrequency, frequency);
    // Save for next update
    prevAvgVoltage = avgVoltage;
    prevTotalCurrent = totalCurrent;
    prevTotalPower = totalPower;
    prevFrequency = frequency;

    // Update individual value displays
    updateIndividualValues(data);
}

function updateIndividualValues(data) {
    // Update voltage values (only if not paused)
    if (!window.isValuesPaused || !window.isValuesPaused('voltage')) {
        const voltage1Element = document.getElementById('voltage1Value');
        const voltage2Element = document.getElementById('voltage2Value');
        const voltage3Element = document.getElementById('voltage3Value');

        if (voltage1Element) voltage1Element.textContent = `${data.voltage_1?.toFixed(2) || '--'} V`;
        if (voltage2Element) voltage2Element.textContent = `${data.voltage_2?.toFixed(2) || '--'} V`;
        if (voltage3Element) voltage3Element.textContent = `${data.voltage_3?.toFixed(2) || '--'} V`;
    }

    // Update current values (only if not paused)
    if (!window.isValuesPaused || !window.isValuesPaused('current')) {
        const current1Element = document.getElementById('current1Value');
        const current2Element = document.getElementById('current2Value');
        const current3Element = document.getElementById('current3Value');

        if (current1Element) current1Element.textContent = `${data.current_1?.toFixed(2) || '--'} A`;
        if (current2Element) current2Element.textContent = `${data.current_2?.toFixed(2) || '--'} A`;
        if (current3Element) current3Element.textContent = `${data.current_3?.toFixed(2) || '--'} A`;
    }

    // Update KVA values (only if not paused)
    if (!window.isValuesPaused || !window.isValuesPaused('kva')) {
        const kvaValuesContentElement = document.getElementById('kvaValuesContent');
        if (kvaValuesContentElement) {
            kvaValuesContentElement.innerHTML = `
                <div class="value-row">
                    <span class="phase-label">L1:</span>
                    <span class="phase-value">${data.kva_1?.toFixed(3) || '--'} kVA</span>
                </div>
                <div class="value-row">
                    <span class="phase-label">L2:</span>
                    <span class="phase-value">${data.kva_2?.toFixed(3) || '--'} kVA</span>
                </div>
                <div class="value-row">
                    <span class="phase-label">L3:</span>
                    <span class="phase-value">${data.kva_3?.toFixed(3) || '--'} kVA</span>
                </div>
            `;
        }
    }

    // Update frequency value (only if not paused)
    if (!window.isValuesPaused || !window.isValuesPaused('frequency')) {
        const frequencyValueContentElement = document.getElementById('frequencyValueContent');
        if (frequencyValueContentElement) {
            frequencyValueContentElement.innerHTML = `
                <div class="value-row">
                    <span class="phase-label">Frequency:</span>
                    <span class="phase-value">${data.frequency?.toFixed(2) || '--'} Hz</span>
                </div>
            `;
        }
    }
}

function showNotification(msg, type = 'info') {
    const container = document.getElementById('notificationContainer');
    const note = document.createElement('div');
    note.className = `notification ${type}`;
    note.textContent = msg;
    container.appendChild(note);
    setTimeout(() => note.remove(), 3000);
}

function refreshDashboard() {
    fetchLatestData().then(data => {
        if (!data) {
            showNotification('Failed to fetch data from backend', 'error');
            return;
        }
        updateSummaryCards(data);
        // TODO: update charts with new data
    });
}

// --- DASHBOARD CHART INITIALIZATION AND LIVE DATA UPDATE ---

document.addEventListener('DOMContentLoaded', function () {
    // Chart instances
    let voltageChart, currentChart, kvaChart, frequencyChart;

    // Helper to get color scheme
    function getColors() {
        const theme = document.getElementById('chartTheme')?.value || 'professional';
        return window.colorSchemes?.[theme]?.primary || ['#0056b3', '#28a745', '#dc3545'];
    }

    // Create all charts
    function initCharts() {
        const colors = getColors();
        voltageChart = window.createProfessionalChart('voltageChart', ['Voltage 1', 'Voltage 2', 'Voltage 3'], colors, 'Voltage (V)', {}, 'voltage');
        currentChart = window.createProfessionalChart('currentChart', ['Current 1', 'Current 2', 'Current 3'], colors, 'Current (A)', {}, 'current');
        kvaChart = window.createProfessionalChart('kvaChart', ['KVA 1', 'KVA 2', 'KVA 3'], colors, 'KVA', {}, 'kva');
        frequencyChart = window.createProfessionalChart('frequencyChart', ['Frequency'], colors, 'Frequency (Hz)', {}, 'frequency');
    }

    // Update all charts with new data
    function updateCharts(data) {
        if (!data) return;
        const ts = new Date(data.timestamp);
        window.updateProfessionalChart(voltageChart, ts, [data.voltage_1, data.voltage_2, data.voltage_3]);
        window.updateProfessionalChart(currentChart, ts, [data.current_1, data.current_2, data.current_3]);
        window.updateProfessionalChart(kvaChart, ts, [data.kva_1, data.kva_2, data.kva_3]);
        window.updateProfessionalChart(frequencyChart, ts, [data.frequency]);
        // Instant values
        document.getElementById('voltageValues').textContent = `L1: ${data.voltage_1}V | L2: ${data.voltage_2}V | L3: ${data.voltage_3}V`;
        document.getElementById('currentValues').textContent = `L1: ${data.current_1}A | L2: ${data.current_2}A | L3: ${data.current_3}A`;
        document.getElementById('kvaValues').textContent = `L1: ${data.kva_1} | L2: ${data.kva_2} | L3: ${data.kva_3}`;
        document.getElementById('frequencyValue').textContent = `${data.frequency} Hz`;
    }

    // Fetch latest data from backend
    function fetchLatestData() {
        return fetch('../backend/get_latest_data.php')
            .then(r => r.json())
            .catch(() => null);
    }

    // Update summary cards
    function updateSummaryCards(data) {
        if (!data) return;
        // document.getElementById('avgVoltage').textContent = data.voltage_1 && data.voltage_2 && data.voltage_3 ? (((data.voltage_1 + data.voltage_2 + data.voltage_3) / 3).toFixed(2) + ' V') : '-- V';
        // document.getElementById('totalCurrent').textContent = data.current_1 && data.current_2 && data.current_3 ? ((data.current_1 + data.current_2 + data.current_3).toFixed(2) + ' A') : '-- A';
        // document.getElementById('totalPower').textContent = data.total_kw ? (data.total_kw + ' kW') : '-- kW';
        // document.getElementById('freqValue').textContent = data.frequency ? (data.frequency + ' Hz') : '-- Hz';
        // document.getElementById('lastUpdatedTime').textContent = data.timestamp || 'N/A';
    }

    // Notification helper
    function showNotification(msg, type = 'info') {
        const container = document.getElementById('notificationContainer');
        const note = document.createElement('div');
        note.className = `notification ${type}`;
        note.textContent = msg;
        container.appendChild(note);
        setTimeout(() => note.remove(), 3000);
    }

    // Main refresh function
    function refreshDashboard() {
        fetchLatestData().then(data => {
            if (!data) {
                showNotification('Failed to fetch data from backend', 'error');
                return;
            }
            updateSummaryCards(data);
            updateCharts(data);
        });
    }

    // Auto-refresh based on settings
    let refreshInterval = 2000;
    function applySettings() {
        const rate = parseInt(document.getElementById('refreshRate').value, 10);
        refreshInterval = Math.max(1000, rate * 1000);
    }
    document.getElementById('refreshRate').addEventListener('change', applySettings);

    // Initialize everything
    initCharts();
    applySettings();
    refreshDashboard();
    setInterval(refreshDashboard, refreshInterval);

    // --- BUTTONS & INTERACTIONS ---

    // Theme toggle
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-theme');
            // Save theme preference
            localStorage.setItem('dashboard-theme', document.body.classList.contains('dark-theme') ? 'dark' : 'light');
        });
        // Load theme preference
        if (localStorage.getItem('dashboard-theme') === 'dark') {
            document.body.classList.add('dark-theme');
        }
    }

    // Sidebar navigation
    const dashboardLink = document.getElementById('dashboardLink');
    const historyLink = document.getElementById('historyLink');
    const settingsLink = document.getElementById('settingsLink');
    const settingsModal = document.getElementById('settingsModal');

    if (dashboardLink) dashboardLink.addEventListener('click', e => { e.preventDefault(); window.location.href = 'advanced_dashboard.php'; });
    if (historyLink) historyLink.addEventListener('click', e => { e.preventDefault(); window.location.href = 'history.php'; });
    if (settingsLink && settingsModal) settingsLink.addEventListener('click', e => {
        e.preventDefault();
        settingsModal.classList.add('active');
    });

    // Settings modal close
    settingsModal?.querySelector('.close-modal')?.addEventListener('click', () => settingsModal.classList.remove('active'));

    // Save/Reset settings
    const saveSettingsBtn = document.getElementById('saveSettings');
    const resetSettingsBtn = document.getElementById('resetSettings');
    if (saveSettingsBtn) saveSettingsBtn.addEventListener('click', () => {
        settingsModal.classList.remove('active');
        showNotification('Settings saved!', 'success');
        applySettings();
    });
    if (resetSettingsBtn) resetSettingsBtn.addEventListener('click', () => {
        document.getElementById('refreshRate').value = 2;
        document.getElementById('timeWindow').value = 1;
        document.getElementById('decimalPlaces').value = 3;
        document.getElementById('chartTheme').value = 'professional';
        document.getElementById('lineThickness').value = 0.5;
        document.getElementById('showGridLines').checked = true;
        document.getElementById('voltageAlertHigh').value = 450;
        document.getElementById('voltageAlertLow').value = 350;
        document.getElementById('frequencyAlertHigh').value = 55;
        document.getElementById('frequencyAlertLow').value = 45;
        showNotification('Settings reset to defaults.', 'info');
        applySettings();
    });

    // Export modal
    const exportModal = document.getElementById('exportModal');
    const exportDataBtn = document.getElementById('exportDataBtn');
    const cancelExportBtn = document.getElementById('cancelExport');
    const confirmExportBtn = document.getElementById('confirmExport');
    if (exportDataBtn && exportModal) exportDataBtn.addEventListener('click', () => exportModal.classList.add('active'));
    if (cancelExportBtn && exportModal) cancelExportBtn.addEventListener('click', () => exportModal.classList.remove('active'));
    if (confirmExportBtn && exportModal) confirmExportBtn.addEventListener('click', () => {
        exportModal.classList.remove('active');
        showNotification('Export started! (Demo only)', 'success');
        // TODO: Implement actual export logic
    });

    // Export custom date range toggle
    const exportTimeRange = document.getElementById('exportTimeRange');
    const customDateRange = document.getElementById('customDateRange');
    if (exportTimeRange && customDateRange) {
        exportTimeRange.addEventListener('change', () => {
            customDateRange.style.display = exportTimeRange.value === 'custom' ? 'block' : 'none';
        });
    }

    // Fullscreen widget logic
    const fullscreenWidget = document.getElementById('fullscreenWidget');
    const fullscreenClose = document.getElementById('fullscreenClose');
    if (fullscreenClose && fullscreenWidget) fullscreenClose.addEventListener('click', () => fullscreenWidget.style.display = 'none');

    window.toggleWidgetExpand = function(widgetId) {
        const widget = document.getElementById(widgetId);
        if (!widget) return;
        fullscreenWidget.style.display = 'flex';
        const content = widget.querySelector('.widget-content');
        const title = widget.querySelector('.widget-title h3').textContent;
        const icon = widget.querySelector('.widget-title .material-icons-round').textContent;
        // Move the canvas node to fullscreen
        const canvas = content.querySelector('canvas');
        if (canvas) {
            document.getElementById('fullscreenContent').innerHTML = '';
            document.getElementById('fullscreenContent').appendChild(canvas);
        } else {
            document.getElementById('fullscreenContent').innerHTML = content.innerHTML;
        }
        document.getElementById('fullscreenTitle').textContent = title;
        document.getElementById('fullscreenIcon').textContent = icon;
        // When closing fullscreen, move canvas back
        fullscreenClose.onclick = function() {
            fullscreenWidget.style.display = 'none';
            if (canvas) {
                content.appendChild(canvas);
            }
        };
    };

    // Chart controls (pause, auto-scroll, scroll)
    window.togglePause = function(type) {
        const chartMap = {
            voltage: voltageChart,
            current: currentChart,
            kva: kvaChart,
            frequency: frequencyChart
        };
        const chart = chartMap[type];
        if (!chart) return;
        chart._paused = !chart._paused;
        const btn = document.getElementById(type + 'Pause');
        if (btn) {
            btn.setAttribute('data-paused', chart._paused ? 'true' : 'false');
            btn.querySelector('.material-icons-round').textContent = chart._paused ? 'play_arrow' : 'pause';
        }
    };
    window.toggleAutoScroll = function(type) {
        const chartMap = {
            voltage: voltageChart,
            current: currentChart,
            kva: kvaChart,
            frequency: frequencyChart
        };
        const chart = chartMap[type];
        if (!chart) return;
        chart._autoScroll = !chart._autoScroll;
        const btn = document.getElementById(type + 'AutoScroll');
        if (btn) {
            btn.setAttribute('data-auto-scroll', chart._autoScroll ? 'true' : 'false');
        }
    };
    window.scrollChartBackward = function(chart, seconds) {
        chart = getChartInstance(chart);
        if (!chart || !chart.options || !chart.options.scales || !chart.options.scales.x) return;
        const xScale = chart.options.scales.x;
        const min = xScale.min ? new Date(xScale.min).getTime() : null;
        const max = xScale.max ? new Date(xScale.max).getTime() : null;
        const shift = seconds * 1000;
        if (min !== null && max !== null) {
            xScale.min = new Date(min - shift);
            xScale.max = new Date(max - shift);
        } else if (chart.data.labels && chart.data.labels.length > 0) {
            // If no min/max set, set them based on current data
            const last = new Date(chart.data.labels[chart.data.labels.length - 1]).getTime();
            xScale.max = new Date(last - shift / 2);
            xScale.min = new Date(xScale.max - shift);
        }
        chart.update('none');
    };

    // Shift chart view right (forward in time)
    window.scrollChartForward = function(chart, seconds) {
        chart = getChartInstance(chart);
        if (!chart || !chart.options || !chart.options.scales || !chart.options.scales.x) return;
        const xScale = chart.options.scales.x;
        const min = xScale.min ? new Date(xScale.min).getTime() : null;
        const max = xScale.max ? new Date(xScale.max).getTime() : null;
        const shift = seconds * 1000;
        if (min !== null && max !== null) {
            xScale.min = new Date(min + shift);
            xScale.max = new Date(max + shift);
        }
        chart.update('none');
    };

    // Auto range (reset x-axis to show latest data)
    function autoRangeChart(chart) {
        chart = getChartInstance(chart);
        if (!chart || !chart.options || !chart.options.scales || !chart.options.scales.x) return;
        chart.options.scales.x.min = undefined;
        chart.options.scales.x.max = undefined;
        chart.update('none');
    }

    // Attach auto range to button
    const autoRangeBtn = document.getElementById('autoRangeBtn');
    if (autoRangeBtn) {
        autoRangeBtn.addEventListener('click', function() {
            autoRangeChart(voltageChart);
            autoRangeChart(currentChart);
            autoRangeChart(kvaChart);
            autoRangeChart(frequencyChart);
        });
    }

    // --- PATCH CHART UPDATE TO HONOR PAUSE ---
    // Patch updateCharts to skip update if paused
    const originalUpdateCharts = updateCharts;
    function updateChartsPatched(data) {
        if (!data) return;
        if (voltageChart && voltageChart._paused) return;
        if (currentChart && currentChart._paused) return;
        if (kvaChart && kvaChart._paused) return;
        if (frequencyChart && frequencyChart._paused) return;
        originalUpdateCharts(data);
    }
    // Replace updateCharts with patched version
    updateCharts = updateChartsPatched;

    // --- TOP NAVIGATION BUTTONS ---
    // Shift all charts left/right
    const prevHourBtn = document.getElementById('prevHourBtn');
    const nextHourBtn = document.getElementById('nextHourBtn');
    const showAllDataBtn = document.getElementById('showAllDataBtn');
    if (prevHourBtn) prevHourBtn.addEventListener('click', function() {
        window.scrollChartBackward(voltageChart, 3600);
        window.scrollChartBackward(currentChart, 3600);
        window.scrollChartBackward(kvaChart, 3600);
        window.scrollChartBackward(frequencyChart, 3600);
    });
    if (nextHourBtn) nextHourBtn.addEventListener('click', function() {
        window.scrollChartForward(voltageChart, 3600);
        window.scrollChartForward(currentChart, 3600);
        window.scrollChartForward(kvaChart, 3600);
        window.scrollChartForward(frequencyChart, 3600);
    });
    if (showAllDataBtn) showAllDataBtn.addEventListener('click', function() {
        // Remove min/max to show all data
        autoRangeChart(voltageChart);
        autoRangeChart(currentChart);
        autoRangeChart(kvaChart);
        autoRangeChart(frequencyChart);
    });

    // --- PAUSE BUFFERING LOGIC ---
    // Buffers for each chart type
    const chartBuffers = {
        voltage: [],
        current: [],
        kva: [],
        frequency: []
    };
    // Patch updateCharts to buffer data if paused
    const originalUpdateCharts2 = originalUpdateCharts || updateCharts;
    function updateChartsBuffered(data) {
        if (!data) return;
        const ts = new Date(data.timestamp);
        // Voltage
        if (voltageChart && voltageChart._paused) {
            chartBuffers.voltage.push({ ts, values: [data.voltage_1, data.voltage_2, data.voltage_3] });
        } else if (voltageChart) {
            // Flush buffer if resuming
            if (chartBuffers.voltage.length > 0) {
                chartBuffers.voltage.forEach(item => window.updateProfessionalChart(voltageChart, item.ts, item.values));
                chartBuffers.voltage = [];
            }
        }
        // Current
        if (currentChart && currentChart._paused) {
            chartBuffers.current.push({ ts, values: [data.current_1, data.current_2, data.current_3] });
        } else if (currentChart) {
            if (chartBuffers.current.length > 0) {
                chartBuffers.current.forEach(item => window.updateProfessionalChart(currentChart, item.ts, item.values));
                chartBuffers.current = [];
            }
        }
        // KVA
        if (kvaChart && kvaChart._paused) {
            chartBuffers.kva.push({ ts, values: [data.kva_1, data.kva_2, data.kva_3] });
        } else if (kvaChart) {
            if (chartBuffers.kva.length > 0) {
                chartBuffers.kva.forEach(item => window.updateProfessionalChart(kvaChart, item.ts, item.values));
                chartBuffers.kva = [];
            }
        }
        // Frequency
        if (frequencyChart && frequencyChart._paused) {
            chartBuffers.frequency.push({ ts, values: [data.frequency] });
        } else if (frequencyChart) {
            if (chartBuffers.frequency.length > 0) {
                chartBuffers.frequency.forEach(item => window.updateProfessionalChart(frequencyChart, item.ts, item.values));
                chartBuffers.frequency = [];
            }
        }
        // Only update visible charts
        if (!(voltageChart && voltageChart._paused)) window.updateProfessionalChart(voltageChart, ts, [data.voltage_1, data.voltage_2, data.voltage_3]);
        if (!(currentChart && currentChart._paused)) window.updateProfessionalChart(currentChart, ts, [data.current_1, data.current_2, data.current_3]);
        if (!(kvaChart && kvaChart._paused)) window.updateProfessionalChart(kvaChart, ts, [data.kva_1, data.kva_2, data.kva_3]);
        if (!(frequencyChart && frequencyChart._paused)) window.updateProfessionalChart(frequencyChart, ts, [data.frequency]);
        // Instant values
        document.getElementById('voltageValues').textContent = `L1: ${data.voltage_1}V | L2: ${data.voltage_2}V | L3: ${data.voltage_3}V`;
        document.getElementById('currentValues').textContent = `L1: ${data.current_1}A | L2: ${data.current_2}A | L3: ${data.current_3}A`;
        document.getElementById('kvaValues').textContent = `L1: ${data.kva_1} | L2: ${data.kva_2} | L3: ${data.kva_3}`;
        document.getElementById('frequencyValue').textContent = `${data.frequency} Hz`;
    }
    updateCharts = updateChartsBuffered;

    // When resuming from pause, flush buffer
    function flushBufferOnResume(type) {
        const chartMap = {
            voltage: voltageChart,
            current: currentChart,
            kva: kvaChart,
            frequency: frequencyChart
        };
        const chart = chartMap[type];
        if (chart && !chart._paused && chartBuffers[type].length > 0) {
            chartBuffers[type].forEach(item => {
                window.updateProfessionalChart(chart, item.ts, item.values);
            });
            chartBuffers[type] = [];
        }
    }
    // Patch togglePause to flush buffer on resume
    const originalTogglePause = window.togglePause;
    window.togglePause = function(type) {
        if (originalTogglePause) originalTogglePause(type);
        flushBufferOnResume(type);
    };

    // Values pause functionality
    const valuesPauseState = {
        voltage: false,
        current: false,
        kva: false,
        frequency: false
    };

    window.toggleValuesPause = function(type) {
        valuesPauseState[type] = !valuesPauseState[type];
        const btn = document.getElementById(type + 'ValuesPause');
        if (btn) {
            btn.setAttribute('data-paused', valuesPauseState[type]);
            const icon = btn.querySelector('.material-icons-round');
            if (icon) {
                icon.textContent = valuesPauseState[type] ? 'play_arrow' : 'pause';
            }
        }
    };

    // Check if values should be updated
    window.isValuesPaused = function(type) {
        return valuesPauseState[type];
    };

    // --- FIX WIDGET BUTTONS AND FULLSCREEN ---
    // Helper to map widgetId to chart and canvas
    function getChartAndCanvasByWidget(widgetId) {
        switch (widgetId) {
            case 'voltageWidget':
                return { chart: voltageChart, canvas: document.getElementById('voltageChart') };
            case 'currentWidget':
                return { chart: currentChart, canvas: document.getElementById('currentChart') };
            case 'kvaWidget':
                return { chart: kvaChart, canvas: document.getElementById('kvaChart') };
            case 'frequencyWidget':
                return { chart: frequencyChart, canvas: document.getElementById('frequencyChart') };
            default:
                return {};
        }
    }

    // Patch widget control buttons to use correct chart instance
    document.querySelectorAll('.widget-controls').forEach(ctrls => {
        const widget = ctrls.closest('.graph-widget');
        if (!widget) return;
        const widgetId = widget.id;
        // Left
        const leftBtn = ctrls.querySelector('button[onclick^="scrollChartBackward"]');
        if (leftBtn) {
            leftBtn.onclick = function() {
                const { chart } = getChartAndCanvasByWidget(widgetId);
                window.scrollChartBackward(chart, 30);
            };
        }
        // Right
        const rightBtn = ctrls.querySelector('button[onclick^="scrollChartForward"]');
        if (rightBtn) {
            rightBtn.onclick = function() {
                const { chart } = getChartAndCanvasByWidget(widgetId);
                window.scrollChartForward(chart, 30);
            };
        }
    });

    // Fix fullscreen to move the canvas instead of copying HTML
    window.toggleWidgetExpand = function(widgetId) {
        const widget = document.getElementById(widgetId);
        if (!widget) return;
        fullscreenWidget.style.display = 'flex';
        const content = widget.querySelector('.widget-content');
        const title = widget.querySelector('.widget-title h3').textContent;
        const icon = widget.querySelector('.widget-title .material-icons-round').textContent;
        // Move the canvas node to fullscreen
        const canvas = content.querySelector('canvas');
        if (canvas) {
            document.getElementById('fullscreenContent').innerHTML = '';
            document.getElementById('fullscreenContent').appendChild(canvas);
        } else {
            document.getElementById('fullscreenContent').innerHTML = content.innerHTML;
        }
        document.getElementById('fullscreenTitle').textContent = title;
        document.getElementById('fullscreenIcon').textContent = icon;
        // When closing fullscreen, move canvas back
        fullscreenClose.onclick = function() {
            fullscreenWidget.style.display = 'none';
            if (canvas) {
                content.appendChild(canvas);
            }
        };
    };

    // --- ENSURE WIDGET ARROW BUTTONS WORK (robust version) ---
    document.querySelectorAll('.widget-controls').forEach(ctrls => {
        const widget = ctrls.closest('.graph-widget');
        if (!widget) return;
        const widgetId = widget.id;
        let chart;
        switch (widgetId) {
            case 'voltageWidget': chart = window.voltageChart; break;
            case 'currentWidget': chart = window.currentChart; break;
            case 'kvaWidget': chart = window.kvaChart; break;
            case 'frequencyWidget': chart = window.frequencyChart; break;
            default: chart = null;
        }
        // Find left/right arrow buttons by icon text
        ctrls.querySelectorAll('button').forEach(btn => {
            const icon = btn.querySelector('.material-icons-round');
            if (!icon) return;
            if (icon.textContent.trim() === 'arrow_back' && chart) {
                btn.onclick = function() { window.scrollChartBackward(chart, 30); };
            }
            if (icon.textContent.trim() === 'arrow_forward' && chart) {
                btn.onclick = function() { window.scrollChartForward(chart, 30); };
            }
        });
    });
});
