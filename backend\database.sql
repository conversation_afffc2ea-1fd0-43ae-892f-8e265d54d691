-- Create the database
CREATE DATABASE IF NOT EXISTS esp32_data;
USE esp32_data;

-- Create the electrical_data table
CREATE TABLE IF NOT EXISTS electrical_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    -- Phase Voltages
    voltage_1 FLOAT,    -- Phase 1 Voltage
    voltage_2 FLOAT,    -- Phase 2 Voltage
    voltage_3 FLOAT,    -- Phase 3 Voltage
    
    -- Phase Currents
    current_1 FLOAT,    -- Phase 1 Current
    current_2 FLOAT,    -- Phase 2 Current
    current_3 FLOAT,    -- Phase 3 Current
    
    -- Power Factors
    pf_1 FLOAT,        -- Phase 1 Power Factor
    pf_2 FLOAT,        -- Phase 2 Power Factor
    pf_3 FLOAT,        -- Phase 3 Power Factor
    
    -- Apparent Power (kVA)
    kva_1 FLOAT,       -- Phase 1 kVA
    kva_2 FLOAT,       -- Phase 2 kVA
    kva_3 FLOAT,       -- Phase 3 kVA
    
    -- Total Power
    total_kva FLOAT,   -- Total kVA
    total_kw FLOAT,    -- Total kW
    total_kvar FLOAT,  -- Total kVAR
    
    frequency FLOAT,   -- System Frequency
    timestamp DATETIME
); 