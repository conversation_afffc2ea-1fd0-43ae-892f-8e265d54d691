<?php
// Server-Sent Events (SSE) endpoint for real-time data streaming
// This provides a continuous stream of electrical data to the frontend

// Set headers for SSE
header('Content-Type: text/event-stream');
header('Cache-Control: no-cache');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Headers: Cache-Control');

// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";
$password = "";
$dbname = "esp32_data";

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    // Send error event
    echo "event: error\n";
    echo "data: " . json_encode(["error" => "Database connection failed"]) . "\n\n";
    exit;
}

// Function to send SSE data
function sendSSEData($eventType, $data) {
    echo "event: $eventType\n";
    echo "data: " . json_encode($data) . "\n\n";
    ob_flush();
    flush();
}

// Function to get latest data
function getLatestData($conn) {
    $sql = "SELECT * FROM electrical_data ORDER BY timestamp DESC LIMIT 1";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        
        // Convert timestamp to IST
        $timestamp = new DateTime($row["timestamp"]);
        $timestamp_ist = $timestamp->format('Y-m-d H:i:s');
        
        return [
            "voltage_1" => floatval($row["voltage_1"]),
            "voltage_2" => floatval($row["voltage_2"]),
            "voltage_3" => floatval($row["voltage_3"]),
            "current_1" => floatval($row["current_1"]),
            "current_2" => floatval($row["current_2"]),
            "current_3" => floatval($row["current_3"]),
            "pf_1" => floatval($row["pf_1"]),
            "pf_2" => floatval($row["pf_2"]),
            "pf_3" => floatval($row["pf_3"]),
            "kva_1" => floatval($row["kva_1"]),
            "kva_2" => floatval($row["kva_2"]),
            "kva_3" => floatval($row["kva_3"]),
            "total_kva" => floatval($row["total_kva"]),
            "total_kw" => floatval($row["total_kw"]),
            "total_kvar" => floatval($row["total_kvar"]),
            "frequency" => floatval($row["frequency"]),
            "timestamp" => $timestamp_ist,
            "id" => intval($row["id"])
        ];
    }
    
    return null;
}

// Send initial connection event
sendSSEData('connected', ['message' => 'SSE connection established', 'time' => date('Y-m-d H:i:s')]);

// Keep track of last sent data ID to avoid duplicates
$lastDataId = 0;
$connectionStartTime = time();
$heartbeatInterval = 30; // Send heartbeat every 30 seconds
$lastHeartbeat = time();

// Main SSE loop
while (true) {
    // Check if client disconnected
    if (connection_aborted()) {
        break;
    }
    
    // Get latest data
    $latestData = getLatestData($conn);
    
    if ($latestData && $latestData['id'] > $lastDataId) {
        // New data available, send it
        sendSSEData('data', $latestData);
        $lastDataId = $latestData['id'];
        
        // Send performance metrics
        $performanceData = [
            'connection_duration' => time() - $connectionStartTime,
            'last_update' => date('Y-m-d H:i:s'),
            'data_id' => $lastDataId
        ];
        sendSSEData('performance', $performanceData);
    }
    
    // Send heartbeat to keep connection alive
    if (time() - $lastHeartbeat >= $heartbeatInterval) {
        sendSSEData('heartbeat', [
            'time' => date('Y-m-d H:i:s'),
            'connection_duration' => time() - $connectionStartTime
        ]);
        $lastHeartbeat = time();
    }
    
    // Sleep for 500ms to reduce server load while maintaining responsiveness
    usleep(500000); // 500ms = 500,000 microseconds
}

// Close database connection
$conn->close();
?>
