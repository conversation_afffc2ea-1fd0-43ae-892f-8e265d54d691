#include <ModbusRTU.h>
#include <Wire.h>
#include <Adafruit_SSD1306.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>

// OLED settings
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, -1);

// Modbus settings
#define RX_PIN 3
#define TX_PIN 5
#define RE_DE_PIN 4
HardwareSerial ModbusSerial(1);
ModbusRTU mb;
uint16_t val[100];

// WiFi credentials
const char* ssid = "kavan";
const char* password = "kavan@113";

// Server URLs
const char* serverUrl = "http://10.84.57.65/online%20data%20logger/backend/receive_data.php";
const char* testUrl = "http://10.84.57.65/online%20data%20logger/backend/test_connection.php";

// Electrical Parameters
float voltage12, voltage23, voltage31;
float pf1, pf2, pf3;
float ia, ib, ic;
float kw, kva, kvar;
float kva1, kva2, kva3;
float frequency;

// Performance optimization variables
int lastHttpResponseCode = 0;
bool serverConnected = false;
HTTPClient http; // Reuse HTTP client for better performance
unsigned long lastConnectionCheck = 0;
const unsigned long CONNECTION_CHECK_INTERVAL = 30000; // Check connection every 30 seconds
bool httpClientInitialized = false;

// Timing optimization
const unsigned long MODBUS_INTERVAL = 500;  // Reduced from 1000ms to 500ms
const unsigned long SEND_INTERVAL = 500;    // Reduced from 1000ms to 500ms
const unsigned long OLED_UPDATE_INTERVAL = 1000; // Keep OLED updates at 1 second

float InttoFloat(uint16_t Data0, uint16_t Data1) {
  float x;
  unsigned long* p = (unsigned long*)&x;
  *p = (unsigned long)Data0 << 16 | Data1;
  return x;
}

int getBatteryPercentage() {
  float batteryVoltage = analogRead(A0) * 3.3 / 4095 * 2;
  const float minVoltage = 3.2;
  const float maxVoltage = 4.2;
  int percentage = (int)((batteryVoltage - minVoltage) / (maxVoltage - minVoltage) * 100);
  return constrain(percentage, 0, 100);
}

void updateOLEDConnecting(bool wifiStatus, bool serverStatus) {
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(15, 0);
  display.println("Online Data Logger");

  display.setCursor(0, 20);
  display.print("WiFi: ");
  display.println(wifiStatus ? "Connected" : "Connecting...");

  display.setCursor(0, 30);
  display.print("Server: ");
  display.println(serverStatus ? "Connected" : "Connecting...");

  display.setCursor(0, 50);
  display.print("Battery: ");
  display.print(getBatteryPercentage());
  display.println("%");

  display.display();
}

void updateOLED() {
  static uint8_t scrollIndex = 0;
  display.clearDisplay();
  display.setTextSize(1);
  display.setCursor(0, 0);
  display.setTextColor(SSD1306_WHITE);
  display.println(" Online Data Logger ");

  display.setCursor(0, 10);
  display.print("WiFi: Connected");

  display.setCursor(0, 20);
  display.print("Server: ");
  display.println(lastHttpResponseCode >= 200 && lastHttpResponseCode < 300 ? "Connected" : "Error");

  display.setCursor(0, 30);
  display.print("Battery: ");
  display.print(getBatteryPercentage());
  display.println("%");

  display.setTextSize(2);
  display.setCursor(0, 44);
  switch (scrollIndex) {
    case 0: display.printf("V1N:%.2f", voltage12); break;
    case 1: display.printf("V2N:%.2f", voltage23); break;
    case 2: display.printf("V3N:%.2f", voltage31); break;
    case 3: display.printf("IA:%.2f", ia); break;
    case 4: display.printf("IB:%.2f", ib); break;
    case 5: display.printf("IC:%.2f", ic); break;
    case 6: display.printf("KVA:%.2f", kva); break;
    case 7: display.printf("KW:%.2f", kw); break;
    case 8: display.printf("Hz:%.2f", frequency); break;
    case 9: display.printf("PF1:%.2f", pf1); break;
    case 10: display.printf("PF2:%.2f", pf2); break;
    case 11: display.printf("PF3:%.2f", pf3); break;
  }
  scrollIndex = (scrollIndex + 1) % 12;
  display.display();
}

bool testServerConnection() {
  if (WiFi.status() != WL_CONNECTED) return false;

  // Only check connection periodically to reduce overhead
  if (millis() - lastConnectionCheck < CONNECTION_CHECK_INTERVAL) {
    return serverConnected;
  }

  HTTPClient testHttp;
  testHttp.begin(testUrl);
  testHttp.setTimeout(3000); // Reduced timeout for faster response
  int httpCode = testHttp.GET();
  testHttp.end();

  lastConnectionCheck = millis();
  bool connected = (httpCode >= 200 && httpCode < 300);
  serverConnected = connected;
  return connected;
}

void initializeHttpClient() {
  if (!httpClientInitialized) {
    http.begin(serverUrl);
    http.setTimeout(5000); // Reduced timeout for faster response
    http.addHeader("Content-Type", "application/json");
    http.setReuse(true); // Enable connection reuse for better performance
    httpClientInitialized = true;
  }
}

void sendDataToServer() {
  if (WiFi.status() != WL_CONNECTED) return;

  // Initialize HTTP client if not done already
  initializeHttpClient();

  // Use ArduinoJson for more efficient JSON creation
  StaticJsonDocument<512> doc;
  doc["voltage_1"] = voltage12;
  doc["voltage_2"] = voltage23;
  doc["voltage_3"] = voltage31;
  doc["current_1"] = ia;
  doc["current_2"] = ib;
  doc["current_3"] = ic;
  doc["pf_1"] = pf1;
  doc["pf_2"] = pf2;
  doc["pf_3"] = pf3;
  doc["kva_1"] = kva1;
  doc["kva_2"] = kva2;
  doc["kva_3"] = kva3;
  doc["total_kva"] = kva;
  doc["total_kw"] = kw;
  doc["total_kvar"] = kvar;
  doc["frequency"] = frequency;

  String jsonData;
  serializeJson(doc, jsonData);

  int httpResponseCode = http.POST(jsonData);
  lastHttpResponseCode = httpResponseCode;

  // Don't end the connection - reuse it for better performance
  // http.end(); // Commented out to enable connection reuse
}

void setup() {
  Serial.begin(115200);
  Wire.begin(9, 10); // OLED I2C pins

  display.begin(SSD1306_SWITCHCAPVCC, 0x3C);
  display.clearDisplay();
  display.display();

  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(20, 20);
  display.println("Starting...");
  display.display();

  WiFi.begin(ssid, password);

  while (WiFi.status() != WL_CONNECTED || !serverConnected) {
    if (WiFi.status() == WL_CONNECTED) {
      serverConnected = testServerConnection();
    }
    updateOLEDConnecting(WiFi.status() == WL_CONNECTED, serverConnected);
    delay(1000);
  }

  Serial.println("WiFi and Server Connected");

  ModbusSerial.begin(9600, SERIAL_8N1, RX_PIN, TX_PIN);
  mb.begin(&ModbusSerial, RE_DE_PIN);
  mb.master();
}

unsigned long lastSendTime = 0;
unsigned long lastModbusRequestTime = 0;
unsigned long lastOLEDUpdateTime = 0;
bool modbusDataReady = false;
bool newDataAvailable = false;

void loop() {
  mb.task();

  // Request Modbus data more frequently (every 500ms)
  if (!mb.slave() && (millis() - lastModbusRequestTime >= MODBUS_INTERVAL)) {
    mb.readIreg(1, 0x0000, val, 100);
    lastModbusRequestTime = millis();
    modbusDataReady = true;
  }

  // Process and send data every 500ms when Modbus data is ready
  if (millis() - lastSendTime >= SEND_INTERVAL && modbusDataReady) {
    lastSendTime = millis();
    modbusDataReady = false;

    // Process electrical parameters
    voltage12 = InttoFloat(val[0], val[1]);
    voltage23 = InttoFloat(val[2], val[3]);
    voltage31 = InttoFloat(val[4], val[5]);

    ia = InttoFloat(val[16], val[17]);
    ib = InttoFloat(val[18], val[19]);
    ic = InttoFloat(val[20], val[21]);

    kva1 = InttoFloat(val[36], val[37]);
    kva2 = InttoFloat(val[38], val[39]);
    kva3 = InttoFloat(val[40], val[41]);

    pf1 = InttoFloat(val[42], val[43]);
    pf2 = InttoFloat(val[44], val[45]);
    pf3 = InttoFloat(val[46], val[47]);

    kw = InttoFloat(val[52], val[53]);
    kvar = InttoFloat(val[54], val[55]);
    kva = InttoFloat(val[56], val[57]);
    frequency = InttoFloat(val[50], val[51]);

    // Send data to server immediately after processing
    sendDataToServer();
    newDataAvailable = true;
  }

  // Update OLED less frequently to reduce processing overhead
  if (millis() - lastOLEDUpdateTime >= OLED_UPDATE_INTERVAL && newDataAvailable) {
    lastOLEDUpdateTime = millis();
    newDataAvailable = false;
    updateOLED();
  }
}
