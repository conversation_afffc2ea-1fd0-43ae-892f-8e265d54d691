<?php
// Include database connection
require_once 'db_connect.php';

// Log the database connection status
error_log("Database connection status: " . ($conn->connect_error ? 'Failed: ' . $conn->connect_error : 'Success'));
error_log("Connected to database: $database on $host");

// Set headers for JSON response
header('Content-Type: application/json');

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Get parameters from request
$parameter = isset($_GET['parameter']) ? $_GET['parameter'] : '';
$start = isset($_GET['start']) ? $_GET['start'] : '';
$end = isset($_GET['end']) ? $_GET['end'] : '';
$page = isset($_GET['page']) ? intval($_GET['page']) : 1;
$recordsPerPage = isset($_GET['limit']) ? intval($_GET['limit']) : 50; // Number of records per page

// Limit the maximum number of records to prevent performance issues
if ($recordsPerPage > 5000) {
    $recordsPerPage = 5000;
}

// Validate parameters
if (empty($parameter) || empty($start) || empty($end)) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Missing required parameters',
        'data' => []
    ]);
    exit;
}

try {
    // Check if the electrical_data table exists
    $tableCheckQuery = "SHOW TABLES LIKE 'electrical_data'";
    $tableResult = $conn->query($tableCheckQuery);

    if ($tableResult->num_rows == 0) {
        error_log("Table 'electrical_data' does not exist!");
        echo json_encode([
            'status' => 'error',
            'message' => "Table 'electrical_data' does not exist in the database.",
            'data' => []
        ]);
        exit;
    }

    // Calculate offset for pagination
    $offset = ($page - 1) * $recordsPerPage;

    // Get total count for pagination
    $countSql = "SELECT COUNT(*) as total FROM electrical_data WHERE timestamp BETWEEN ? AND ?";
    $countStmt = $conn->prepare($countSql);
    $countStmt->bind_param("ss", $start, $end);
    $countStmt->execute();
    $countResult = $countStmt->get_result();
    $totalRecords = $countResult->fetch_assoc()['total'];
    $totalPages = ceil($totalRecords / $recordsPerPage);

    // Log the query and results for debugging
    error_log("Total records found: $totalRecords for date range $start to $end");

    // Determine which columns to select based on parameter
    $columns = "id, timestamp";
    switch ($parameter) {
        case 'voltage':
            $columns .= ", voltage_1, voltage_2, voltage_3";
            break;
        case 'current':
            $columns .= ", current_1, current_2, current_3";
            break;
        case 'pf':
            $columns .= ", pf_1, pf_2, pf_3";
            break;
        case 'kva':
            $columns .= ", kva_1, kva_2, kva_3";
            break;
        case 'power':
            $columns .= ", total_kw, total_kva, total_kvar";
            break;
        case 'frequency':
            $columns .= ", frequency";
            break;
        default:
            // If parameter is not recognized, select all columns
            $columns = "*";
    }

    // Prepare SQL query to fetch data
    $sql = "SELECT $columns FROM electrical_data WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp ASC LIMIT ? OFFSET ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ssii", $start, $end, $recordsPerPage, $offset);

    // Log the query for debugging
    error_log("Executing query: $sql with params: $start, $end, $recordsPerPage, $offset");

    $stmt->execute();
    $result = $stmt->get_result();

    // Log the number of rows returned
    error_log("Number of rows returned: " . $result->num_rows);

    $data = [];
    while ($row = $result->fetch_assoc()) {
        $data[] = $row;
    }

    echo json_encode([
        'status' => 'success',
        'message' => 'Data retrieved successfully',
        'data' => $data,
        'totalRecords' => $totalRecords,
        'totalPages' => $totalPages,
        'currentPage' => $page,
        'recordsPerPage' => $recordsPerPage
    ]);

} catch (Exception $e) {
    echo json_encode([
        'status' => 'error',
        'message' => 'Database error: ' . $e->getMessage(),
        'data' => []
    ]);
}

// Close connection
$conn->close();
?>
