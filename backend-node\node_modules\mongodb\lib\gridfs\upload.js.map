{"version": 3, "file": "upload.js", "sourceRoot": "", "sources": ["../../src/gridfs/upload.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAElC,kCAAkD;AAElD,+DAA8D;AAC9D,oCAKkB;AAClB,wCAAgD;AAChD,oCAA6E;AAE7E,sDAAkD;AAqClD;;;;;GAKG;AACH,MAAa,uBAAwB,SAAQ,iBAAQ;IAuDnD;;;;;OAKG;IACH,YAAY,MAAoB,EAAE,QAAgB,EAAE,OAAwC;QAC1F,KAAK,EAAE,CAAC;QAzBV;;;;;;;;;;;;;WAaG;QACH,eAAU,GAAsB,IAAI,CAAC;QAanC,OAAO,GAAG,OAAO,IAAI,EAAE,CAAC;QACxB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,iBAAiB,CAAC;QACzC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,gBAAgB,CAAC;QACvC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG,4BAAY,CAAC,WAAW,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC;QACvF,gCAAgC;QAChC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC;QAElB,IAAI,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,eAAQ,EAAE,CAAC;QACnD,qDAAqD;QACrD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,cAAc,CAAC;QACrF,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACpD,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;QAChB,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;QACX,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;QACb,IAAI,CAAC,KAAK,GAAG;YACX,SAAS,EAAE,KAAK;YAChB,mBAAmB,EAAE,CAAC;YACtB,OAAO,EAAE,KAAK;YACd,OAAO,EAAE,KAAK;SACf,CAAC;QAEF,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI;YAC3B,IAAI,CAAC,cAAc,GAAG,IAAI,4BAAkB,CAAC;gBAC3C,SAAS,EAAE,OAAO,CAAC,SAAS;gBAC5B,wBAAwB,EAAE,IAAA,6BAAqB,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,CAAC;qBACzE,wBAAwB;aAC5B,CAAC,CAAC;IACP,CAAC;IAED;;;;OAIG;IACM,UAAU,CAAC,QAAwC;QAC1D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB,EAAE,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB,GAAG,IAAI,CAAC;YAE5C,YAAY,CAAC,IAAI,CAAC,CAAC,IAAI,CACrB,GAAG,EAAE;gBACH,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,GAAG,IAAI,CAAC;gBACpC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;gBAC1B,QAAQ,EAAE,CAAC;YACb,CAAC,EACD,KAAK,CAAC,EAAE;gBACN,IAAI,KAAK,YAAY,kCAA0B,EAAE,CAAC;oBAChD,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;gBAC5C,CAAC;gBACD,IAAA,mBAAW,EAAC,KAAK,CAAC,CAAC;gBACnB,QAAQ,EAAE,CAAC;YACb,CAAC,CACF,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,OAAO,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACM,MAAM,CACb,KAAsB,EACtB,QAAwB,EACxB,QAAwB;QAExB,OAAO,CAAC,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IAC3C,CAAC;IAED,gBAAgB;IACP,MAAM,CAAC,QAAwC;QACtD,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACzB,OAAO,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QACpC,CAAC;QACD,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC;QAC5B,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC;YACzB,wDAAwD;YACxD,MAAM,IAAI,qBAAa,CAAC,kDAAkD,CAAC,CAAC;QAC9E,CAAC;QAED,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;YACvB,wDAAwD;YACxD,MAAM,IAAI,qBAAa,CAAC,uCAAuC,CAAC,CAAC;QACnE,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;QAC1B,MAAM,eAAe,GAAG,IAAI,CAAC,cAAc,EAAE,yBAAyB,CACpE,0BAA0B,IAAI,CAAC,cAAc,EAAE,SAAS,IAAI,CAC7D,CAAC;QAEF,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC,CAAC;IACtF,CAAC;CACF;AA3KD,0DA2KC;AAED,SAAS,WAAW,CAAC,MAA+B,EAAE,KAAY,EAAE,QAAkB;IACpF,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzB,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC3B,OAAO;IACT,CAAC;IACD,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC;IAC5B,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AACpC,CAAC;AAED,SAAS,cAAc,CAAC,OAAiB,EAAE,CAAS,EAAE,IAAY;IAChE,OAAO;QACL,GAAG,EAAE,IAAI,eAAQ,EAAE;QACnB,QAAQ,EAAE,OAAO;QACjB,CAAC;QACD,IAAI;KACL,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,gBAAgB,CAAC,MAA+B;IAC7D,MAAM,KAAK,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAEpC,IAAI,eAAe,CAAC;IACpB,eAAe,GAAG,MAAM,CAAC,cAAc,EAAE,yBAAyB,CAChE,0BAA0B,MAAM,CAAC,cAAc,EAAE,SAAS,IAAI,CAC/D,CAAC;IAEF,IAAI,OAAO,CAAC;IACZ,IAAI,CAAC;QACH,OAAO,GAAG,MAAM,MAAM,CAAC,MAAM;aAC1B,WAAW,CAAC;YACX,WAAW,EAAE,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,mCAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;YAC7E,SAAS,EAAE,eAAe;SAC3B,CAAC;aACD,OAAO,EAAE,CAAC;IACf,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,kBAAU,IAAI,KAAK,CAAC,IAAI,KAAK,2BAAmB,CAAC,iBAAiB,EAAE,CAAC;YACxF,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,MAAM,cAAc,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC5C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC;YACvE,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,eAAe,GAAG,MAAM,CAAC,cAAc,EAAE,yBAAyB,CAChE,0BAA0B,MAAM,CAAC,cAAc,EAAE,SAAS,IAAI,CAC/D,CAAC;QACF,MAAM,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE;YACrC,GAAG,MAAM,CAAC,YAAY;YACtB,UAAU,EAAE,IAAI;YAChB,MAAM,EAAE,IAAI;YACZ,SAAS,EAAE,eAAe;SAC3B,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,SAAS,SAAS,CAAC,MAA+B,EAAE,QAAkB;IACpE,IAAI,MAAM,CAAC,IAAI,EAAE,CAAC;QAChB,OAAO,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACpC,CAAC;IAED,IAAI,MAAM,CAAC,KAAK,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,mBAAmB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QAC9F,yDAAyD;QACzD,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC;QACnB,yBAAyB;QACzB,MAAM,UAAU,GAAG,cAAc,CAC/B,MAAM,CAAC,EAAE,EACT,MAAM,CAAC,MAAM,EACb,MAAM,CAAC,cAAc,EACrB,MAAM,CAAC,QAAQ,EACf,MAAM,CAAC,OAAO,CAAC,WAAW,EAC1B,MAAM,CAAC,OAAO,CAAC,OAAO,EACtB,MAAM,CAAC,OAAO,CAAC,QAAQ,CACxB,CAAC;QAEF,IAAI,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;YAChC,OAAO;QACT,CAAC;QAED,MAAM,eAAe,GAAG,MAAM,CAAC,cAAc,EAAE,eAAe,CAAC;QAC/D,IAAI,eAAe,IAAI,IAAI,IAAI,eAAe,IAAI,CAAC,EAAE,CAAC;YACpD,OAAO,WAAW,CAChB,MAAM,EACN,IAAI,kCAA0B,CAC5B,0BAA0B,MAAM,CAAC,cAAc,EAAE,SAAS,IAAI,CAC/D,EACD,QAAQ,CACT,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,KAAK;aACT,SAAS,CAAC,UAAU,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC;aACxF,IAAI,CACH,GAAG,EAAE;YACH,MAAM,CAAC,UAAU,GAAG,UAAU,CAAC;YAC/B,QAAQ,EAAE,CAAC;QACb,CAAC,EACD,KAAK,CAAC,EAAE;YACN,OAAO,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;QAC9C,CAAC,CACF,CAAC;QACJ,OAAO;IACT,CAAC;IAED,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AAC7B,CAAC;AAED,KAAK,UAAU,YAAY,CAAC,MAA+B;IACzD,IAAI,eAAe,GAAG,MAAM,CAAC,cAAc,EAAE,yBAAyB,CACpE,0BAA0B,MAAM,CAAC,cAAc,EAAE,SAAS,IAAI,CAC/D,CAAC;IACF,MAAM,GAAG,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CACpC,EAAE,EACF;QACE,UAAU,EAAE,EAAE,GAAG,EAAE,CAAC,EAAE;QACtB,SAAS,EAAE,eAAe;KAC3B,CACF,CAAC;IACF,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QAChB,+EAA+E;QAC/E,OAAO;IACT,CAAC;IAED,MAAM,KAAK,GAAG,EAAE,QAAQ,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;IAE7C,IAAI,OAAO,CAAC;IACZ,eAAe,GAAG,MAAM,CAAC,cAAc,EAAE,yBAAyB,CAChE,0BAA0B,MAAM,CAAC,cAAc,EAAE,SAAS,IAAI,CAC/D,CAAC;IACF,MAAM,kBAAkB,GAAG;QACzB,WAAW,EAAE,eAAe,IAAI,IAAI,CAAC,CAAC,CAAC,mCAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;QAC7E,SAAS,EAAE,eAAe;KAC3B,CAAC;IACF,IAAI,CAAC;QACH,OAAO,GAAG,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC,OAAO,EAAE,CAAC;IACzE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,KAAK,YAAY,kBAAU,IAAI,KAAK,CAAC,IAAI,KAAK,2BAAmB,CAAC,iBAAiB,EAAE,CAAC;YACxF,OAAO,GAAG,EAAE,CAAC;QACf,CAAC;aAAM,CAAC;YACN,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,MAAM,YAAY,GAAG,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;QAC1C,MAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,QAAQ,KAAK,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YAChF,OAAO,IAAI,CAAC;QACd,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC,CAAC,CAAC;IAEH,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,eAAe,GAAG,MAAM,CAAC,cAAc,EAAE,yBAAyB,CAChE,0BAA0B,MAAM,CAAC,cAAc,EAAE,SAAS,IAAI,CAC/D,CAAC;QAEF,MAAM,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,EAAE,UAAU,EAAE,KAAK,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC,CAAC;IAC3F,CAAC;IAED,MAAM,gBAAgB,CAAC,MAAM,CAAC,CAAC;AACjC,CAAC;AAED,SAAS,cAAc,CACrB,GAAa,EACb,MAAc,EACd,SAAiB,EACjB,QAAgB,EAChB,WAAoB,EACpB,OAAkB,EAClB,QAAmB;IAEnB,MAAM,GAAG,GAAe;QACtB,GAAG;QACH,MAAM;QACN,SAAS;QACT,UAAU,EAAE,IAAI,IAAI,EAAE;QACtB,QAAQ;KACT,CAAC;IAEF,IAAI,WAAW,EAAE,CAAC;QAChB,GAAG,CAAC,WAAW,GAAG,WAAW,CAAC;IAChC,CAAC;IAED,IAAI,OAAO,EAAE,CAAC;QACZ,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC;IACxB,CAAC;IAED,IAAI,QAAQ,EAAE,CAAC;QACb,GAAG,CAAC,QAAQ,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,OAAO,CACd,MAA+B,EAC/B,KAAsB,EACtB,QAAwB,EACxB,QAAwB;IAExB,IAAI,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;QAChC,OAAO;IACT,CAAC;IAED,MAAM,QAAQ,GAAG,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;IAE/E,MAAM,CAAC,MAAM,IAAI,QAAQ,CAAC,MAAM,CAAC;IAEjC,6CAA6C;IAC7C,IAAI,MAAM,CAAC,GAAG,GAAG,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC,cAAc,EAAE,CAAC;QACzD,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;QAC7C,MAAM,CAAC,GAAG,IAAI,QAAQ,CAAC,MAAM,CAAC;QAC9B,OAAO,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC3B,OAAO;IACT,CAAC;IAED,sEAAsE;IACtE,cAAc;IACd,IAAI,iBAAiB,GAAG,QAAQ,CAAC,MAAM,CAAC;IACxC,IAAI,cAAc,GAAW,MAAM,CAAC,cAAc,GAAG,MAAM,CAAC,GAAG,CAAC;IAChE,IAAI,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;IAC1D,IAAI,mBAAmB,GAAG,CAAC,CAAC;IAC5B,OAAO,iBAAiB,GAAG,CAAC,EAAE,CAAC;QAC7B,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAM,GAAG,iBAAiB,CAAC;QACxD,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,GAAG,EAAE,WAAW,EAAE,WAAW,GAAG,SAAS,CAAC,CAAC;QACnF,MAAM,CAAC,GAAG,IAAI,SAAS,CAAC;QACxB,cAAc,IAAI,SAAS,CAAC;QAC5B,IAAI,GAAgB,CAAC;QACrB,IAAI,cAAc,KAAK,CAAC,EAAE,CAAC;YACzB,GAAG,GAAG,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;YAE1E,MAAM,eAAe,GAAG,MAAM,CAAC,cAAc,EAAE,eAAe,CAAC;YAC/D,IAAI,eAAe,IAAI,IAAI,IAAI,eAAe,IAAI,CAAC,EAAE,CAAC;gBACpD,OAAO,WAAW,CAChB,MAAM,EACN,IAAI,kCAA0B,CAC5B,0BAA0B,MAAM,CAAC,cAAc,EAAE,SAAS,IAAI,CAC/D,EACD,QAAQ,CACT,CAAC;YACJ,CAAC;YAED,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC;YACnC,EAAE,mBAAmB,CAAC;YAEtB,IAAI,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,MAAM,CAAC,MAAM;iBACV,SAAS,CAAC,GAAG,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC;iBACjF,IAAI,CACH,GAAG,EAAE;gBACH,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC;gBACnC,EAAE,mBAAmB,CAAC;gBAEtB,IAAI,CAAC,mBAAmB,EAAE,CAAC;oBACzB,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;gBAC9B,CAAC;YACH,CAAC,EACD,KAAK,CAAC,EAAE;gBACN,OAAO,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;YAC9C,CAAC,CACF,CAAC;YAEJ,cAAc,GAAG,MAAM,CAAC,cAAc,CAAC;YACvC,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;YACf,EAAE,MAAM,CAAC,CAAC,CAAC;QACb,CAAC;QACD,iBAAiB,IAAI,SAAS,CAAC;QAC/B,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC1D,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CAAC,MAA+B,EAAE,QAAkB;IACvE,6CAA6C;IAC7C,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC;QACrB,OAAO,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IACrC,CAAC;IAED,yEAAyE;IACzE,SAAS;IACT,MAAM,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACzC,MAAM,CAAC,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,GAAG,CAAC,CAAC;IAClD,MAAM,GAAG,GAAG,cAAc,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAEzD,kDAAkD;IAClD,IAAI,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,EAAE,CAAC;QAChC,OAAO;IACT,CAAC;IAED,MAAM,eAAe,GAAG,MAAM,CAAC,cAAc,EAAE,eAAe,CAAC;IAC/D,IAAI,eAAe,IAAI,IAAI,IAAI,eAAe,IAAI,CAAC,EAAE,CAAC;QACpD,OAAO,WAAW,CAChB,MAAM,EACN,IAAI,kCAA0B,CAC5B,0BAA0B,MAAM,CAAC,cAAc,EAAE,SAAS,IAAI,CAC/D,EACD,QAAQ,CACT,CAAC;IACJ,CAAC;IACD,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC;IACnC,MAAM,CAAC,MAAM;SACV,SAAS,CAAC,GAAG,EAAE,EAAE,YAAY,EAAE,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC;SACjF,IAAI,CACH,GAAG,EAAE;QACH,EAAE,MAAM,CAAC,KAAK,CAAC,mBAAmB,CAAC;QACnC,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;IAC9B,CAAC,EACD,KAAK,CAAC,EAAE;QACN,OAAO,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC;IAC9C,CAAC,CACF,CAAC;AACN,CAAC;AAED,SAAS,SAAS,CAAC,MAA+B,EAAE,QAAwB;IAC1E,IAAI,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC;QACzB,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,qBAAa,CAAC,yBAAyB,CAAC,CAAC,CAAC;QACzE,OAAO,IAAI,CAAC;IACd,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}