<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Set headers
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Get server information
$serverInfo = [
    'server_ip' => $_SERVER['SERVER_ADDR'],
    'server_name' => $_SERVER['SERVER_NAME'],
    'remote_addr' => $_SERVER['REMOTE_ADDR'],
    'server_software' => $_SERVER['SERVER_SOFTWARE'],
    'document_root' => $_SERVER['DOCUMENT_ROOT'],
    'script_filename' => $_SERVER['SCRIPT_FILENAME'],
    'request_uri' => $_SERVER['REQUEST_URI'],
    'php_version' => phpversion(),
    'timestamp' => date('Y-m-d H:i:s'),
    'timezone' => date_default_timezone_get()
];

// Check if we can write to the log file
$logFile = 'esp32_request_log.txt';
$canWrite = is_writable(dirname(__FILE__)) || (file_exists($logFile) && is_writable($logFile));
$serverInfo['can_write_log'] = $canWrite;

// Test database connection
$dbInfo = [];
try {
    $servername = "localhost";
    $username = "root";
    $password = "";
    $dbname = "esp32_data";
    
    $conn = new mysqli($servername, $username, $password, $dbname);
    
    if ($conn->connect_error) {
        $dbInfo['status'] = 'error';
        $dbInfo['message'] = 'Connection failed: ' . $conn->connect_error;
    } else {
        $dbInfo['status'] = 'success';
        $dbInfo['message'] = 'Connected to database successfully';
        
        // Check if the table exists
        $result = $conn->query("SHOW TABLES LIKE 'electrical_data'");
        $dbInfo['table_exists'] = $result->num_rows > 0;
        
        if ($dbInfo['table_exists']) {
            // Get row count
            $result = $conn->query("SELECT COUNT(*) as count FROM electrical_data");
            $row = $result->fetch_assoc();
            $dbInfo['row_count'] = $row['count'];
        }
        
        $conn->close();
    }
} catch (Exception $e) {
    $dbInfo['status'] = 'error';
    $dbInfo['message'] = 'Exception: ' . $e->getMessage();
}

// Add database info to server info
$serverInfo['database'] = $dbInfo;

// Return the information as JSON
echo json_encode($serverInfo, JSON_PRETTY_PRINT);
?>
