<?php
// Include database connection
require_once 'db_connect.php';

// Set headers for plain text response
header('Content-Type: text/plain');

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "Database Connection Test\n";
echo "=======================\n\n";

// Test database connection
if ($conn->connect_error) {
    echo "Connection failed: " . $conn->connect_error;
    exit;
} else {
    echo "Database connection successful!\n";
    echo "Connected to: $database on $host\n\n";
}

// Check if the electrical_data table exists
$tableCheckQuery = "SHOW TABLES LIKE 'electrical_data'";
$tableResult = $conn->query($tableCheckQuery);

if ($tableResult->num_rows > 0) {
    echo "Table 'electrical_data' exists.\n\n";
    
    // Count records in the table
    $countQuery = "SELECT COUNT(*) as total FROM electrical_data";
    $countResult = $conn->query($countQuery);
    $totalRecords = $countResult->fetch_assoc()['total'];
    
    echo "Total records in electrical_data: $totalRecords\n\n";
    
    if ($totalRecords > 0) {
        // Get the first and last record timestamps
        $timeQuery = "SELECT MIN(timestamp) as first, MAX(timestamp) as last FROM electrical_data";
        $timeResult = $conn->query($timeQuery);
        $timeData = $timeResult->fetch_assoc();
        
        echo "First record timestamp: " . $timeData['first'] . "\n";
        echo "Last record timestamp: " . $timeData['last'] . "\n\n";
        
        // Get a sample of the data
        echo "Sample data (latest 5 records):\n";
        $sampleQuery = "SELECT * FROM electrical_data ORDER BY timestamp DESC LIMIT 5";
        $sampleResult = $conn->query($sampleQuery);
        
        if ($sampleResult->num_rows > 0) {
            echo "ID\tTimestamp\t\t\tVoltage_1\tCurrent_1\tFrequency\n";
            echo "-------------------------------------------------------------------------\n";
            
            while ($row = $sampleResult->fetch_assoc()) {
                echo $row['id'] . "\t" . 
                     $row['timestamp'] . "\t" . 
                     ($row['voltage_1'] ?? 'N/A') . "\t\t" . 
                     ($row['current_1'] ?? 'N/A') . "\t\t" . 
                     ($row['frequency'] ?? 'N/A') . "\n";
            }
        } else {
            echo "No sample data available.\n";
        }
    } else {
        echo "The table is empty. No records found.\n";
    }
} else {
    echo "Table 'electrical_data' does not exist!\n";
    
    // Show all tables in the database
    echo "\nAvailable tables in the database:\n";
    $allTablesQuery = "SHOW TABLES";
    $allTablesResult = $conn->query($allTablesQuery);
    
    if ($allTablesResult->num_rows > 0) {
        while ($row = $allTablesResult->fetch_row()) {
            echo "- " . $row[0] . "\n";
        }
    } else {
        echo "No tables found in the database.\n";
    }
}

// Close connection
$conn->close();
?>
