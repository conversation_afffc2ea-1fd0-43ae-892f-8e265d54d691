<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Electrical Parameters Monitor</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #007bff;
            color: white;
            font-weight: bold;
        }
        .value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
        }
        .unit {
            font-size: 0.9rem;
            color: #6c757d;
        }
        .last-update {
            font-size: 0.8rem;
            color: #6c757d;
            text-align: right;
        }
        .status-indicator {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-offline {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <h1 class="text-center mb-4">Electrical Parameters Monitor</h1>
        
        <div class="row">
            <!-- System Status -->
            <div class="col-md-12 mb-4">
                <div class="card">
                    <div class="card-header">
                        System Status
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <span class="status-indicator status-online"></span>
                                <span>System Status: Online</span>
                            </div>
                            <div class="col-md-6 last-update">
                                Last Update: <span id="lastUpdate">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Phase Voltages -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        Phase Voltages
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="voltage1">0.00</div>
                                    <div class="unit">V1 (V)</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="voltage2">0.00</div>
                                    <div class="unit">V2 (V)</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="voltage3">0.00</div>
                                    <div class="unit">V3 (V)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Phase Currents -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        Phase Currents
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="current1">0.00</div>
                                    <div class="unit">I1 (A)</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="current2">0.00</div>
                                    <div class="unit">I2 (A)</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="current3">0.00</div>
                                    <div class="unit">I3 (A)</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Power Factors -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        Power Factors
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="pf1">0.00</div>
                                    <div class="unit">PF1</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="pf2">0.00</div>
                                    <div class="unit">PF2</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="pf3">0.00</div>
                                    <div class="unit">PF3</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Apparent Power (kVA) -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header">
                        Apparent Power
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="kva1">0.00</div>
                                    <div class="unit">kVA1</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="kva2">0.00</div>
                                    <div class="unit">kVA2</div>
                                </div>
                            </div>
                            <div class="col-4">
                                <div class="text-center">
                                    <div class="value" id="kva3">0.00</div>
                                    <div class="unit">kVA3</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Total Power -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        Total Power
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="value" id="totalKw">0.00</div>
                                    <div class="unit">Total kW</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="value" id="totalKva">0.00</div>
                                    <div class="unit">Total kVA</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="text-center">
                                    <div class="value" id="totalKvar">0.00</div>
                                    <div class="unit">Total kVAR</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function updateData() {
            fetch('get_latest_data.php')
                .then(response => response.json())
                .then(data => {
                    // Update values
                    document.getElementById('voltage1').textContent = data.voltage_1.toFixed(2);
                    document.getElementById('voltage2').textContent = data.voltage_2.toFixed(2);
                    document.getElementById('voltage3').textContent = data.voltage_3.toFixed(2);
                    
                    document.getElementById('current1').textContent = data.current_1.toFixed(2);
                    document.getElementById('current2').textContent = data.current_2.toFixed(2);
                    document.getElementById('current3').textContent = data.current_3.toFixed(2);
                    
                    document.getElementById('pf1').textContent = data.pf_1.toFixed(2);
                    document.getElementById('pf2').textContent = data.pf_2.toFixed(2);
                    document.getElementById('pf3').textContent = data.pf_3.toFixed(2);
                    
                    document.getElementById('kva1').textContent = data.kva_1.toFixed(2);
                    document.getElementById('kva2').textContent = data.kva_2.toFixed(2);
                    document.getElementById('kva3').textContent = data.kva_3.toFixed(2);
                    
                    document.getElementById('totalKw').textContent = data.total_kw.toFixed(2);
                    document.getElementById('totalKva').textContent = data.total_kva.toFixed(2);
                    document.getElementById('totalKvar').textContent = data.total_kvar.toFixed(2);
                    
                    // Update last update time
                    const now = new Date();
                    document.getElementById('lastUpdate').textContent = now.toLocaleTimeString();
                })
                .catch(error => {
                    console.error('Error fetching data:', error);
                    document.getElementById('lastUpdate').textContent = 'Error updating data';
                });
        }

        // Update data every second
        setInterval(updateData, 1000);
        
        // Initial update
        updateData();
    </script>
</body>
</html> 