<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ESP32 Server Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }
        .card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 15px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 10px 5px;
            cursor: pointer;
            border-radius: 4px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f0f0f0;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .info {
            color: blue;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body>
    <h1>ESP32 Server Connection Test</h1>
    
    <div class="card">
        <h2>Test Connection</h2>
        <p>Click the button below to test the connection to the server:</p>
        <button id="testConnectionBtn">Test Connection</button>
        <div id="connectionResult"></div>
    </div>
    
    <div class="card">
        <h2>Send Test Data</h2>
        <p>Click the button below to send test data to the server:</p>
        <button id="sendTestDataBtn">Send Test Data</button>
        <div id="sendResult"></div>
    </div>
    
    <div class="card">
        <h2>Server Information</h2>
        <button id="getServerInfoBtn">Get Server Info</button>
        <div id="serverInfo"></div>
    </div>
    
    <div class="card">
        <h2>Connection Logs</h2>
        <button id="viewLogsBtn">View Connection Logs</button>
        <pre id="logsContent"></pre>
    </div>
    
    <script>
        document.getElementById('testConnectionBtn').addEventListener('click', async function() {
            const resultDiv = document.getElementById('connectionResult');
            resultDiv.innerHTML = '<p class="info">Testing connection...</p>';
            
            try {
                const response = await fetch('test_connection.php');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                resultDiv.innerHTML = `
                    <p class="success">Connection successful!</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">Connection failed: ${error.message}</p>`;
            }
        });
        
        document.getElementById('sendTestDataBtn').addEventListener('click', async function() {
            const resultDiv = document.getElementById('sendResult');
            resultDiv.innerHTML = '<p class="info">Sending test data...</p>';
            
            const testData = {
                voltage_1: 230 + Math.random() * 10,
                voltage_2: 230 + Math.random() * 10,
                voltage_3: 230 + Math.random() * 10,
                current_1: 5 + Math.random() * 2,
                current_2: 5 + Math.random() * 2,
                current_3: 5 + Math.random() * 2,
                pf_1: 0.9 + Math.random() * 0.1,
                pf_2: 0.9 + Math.random() * 0.1,
                pf_3: 0.9 + Math.random() * 0.1,
                kva_1: 10 + Math.random() * 2,
                kva_2: 10 + Math.random() * 2,
                kva_3: 10 + Math.random() * 2,
                total_kva: 30 + Math.random() * 5,
                total_kw: 27 + Math.random() * 5,
                total_kvar: 13 + Math.random() * 3,
                frequency: 50 + Math.random() * 0.2
            };
            
            try {
                const response = await fetch('receive_data.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                resultDiv.innerHTML = `
                    <p class="success">Test data sent successfully!</p>
                    <p>Data sent:</p>
                    <pre>${JSON.stringify(testData, null, 2)}</pre>
                    <p>Server response:</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<p class="error">Failed to send test data: ${error.message}</p>`;
            }
        });
        
        document.getElementById('getServerInfoBtn').addEventListener('click', async function() {
            const infoDiv = document.getElementById('serverInfo');
            infoDiv.innerHTML = '<p class="info">Getting server information...</p>';
            
            try {
                const response = await fetch('server_info.php');
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Create a table for server information
                let tableHtml = '<h3>Server Information</h3><table>';
                tableHtml += '<tr><th>Property</th><th>Value</th></tr>';
                
                for (const [key, value] of Object.entries(data)) {
                    if (typeof value !== 'object') {
                        tableHtml += `<tr><td>${key}</td><td>${value}</td></tr>`;
                    }
                }
                
                tableHtml += '</table>';
                
                // Add database information if available
                if (data.database) {
                    tableHtml += '<h3>Database Information</h3><table>';
                    tableHtml += '<tr><th>Property</th><th>Value</th></tr>';
                    
                    for (const [key, value] of Object.entries(data.database)) {
                        tableHtml += `<tr><td>${key}</td><td>${value}</td></tr>`;
                    }
                    
                    tableHtml += '</table>';
                }
                
                infoDiv.innerHTML = tableHtml;
            } catch (error) {
                infoDiv.innerHTML = `<p class="error">Failed to get server information: ${error.message}</p>`;
            }
        });
        
        document.getElementById('viewLogsBtn').addEventListener('click', async function() {
            const logsContent = document.getElementById('logsContent');
            logsContent.textContent = 'Loading logs...';
            
            try {
                // Try to fetch connection test logs
                let response = await fetch('connection_test_log.txt');
                if (!response.ok) {
                    // If that fails, try the ESP32 request log
                    response = await fetch('esp32_request_log.txt');
                    if (!response.ok) {
                        throw new Error('No log files found');
                    }
                }
                
                const text = await response.text();
                logsContent.textContent = text || 'No log entries found.';
            } catch (error) {
                logsContent.textContent = `Error loading logs: ${error.message}`;
            }
        });
    </script>
</body>
</html>
