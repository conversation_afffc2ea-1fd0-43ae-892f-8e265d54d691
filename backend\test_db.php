<?php
// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "<h1>Database Connection Test</h1>";

// Database connection parameters
$host = 'localhost';
$username = 'root';  // Default XAMPP username
$password = '';      // Default XAMPP password (empty)
$database = 'power_monitoring';

echo "<p>Attempting to connect to database: $database</p>";

// Create connection
$conn = new mysqli($host, $username, $password, $database);

// Check connection
if ($conn->connect_error) {
    die("<p style='color:red'>Connection failed: " . $conn->connect_error . "</p>");
}

echo "<p style='color:green'>Connection successful!</p>";

// Check if the power_data table exists
$result = $conn->query("SHOW TABLES LIKE 'power_data'");
if ($result->num_rows > 0) {
    echo "<p style='color:green'>Table 'power_data' exists.</p>";
    
    // Count records in the table
    $countResult = $conn->query("SELECT COUNT(*) as count FROM power_data");
    $row = $countResult->fetch_assoc();
    echo "<p>Number of records in power_data: " . $row['count'] . "</p>";
    
    // Get the latest record
    $latestResult = $conn->query("SELECT * FROM power_data ORDER BY timestamp DESC LIMIT 1");
    if ($latestResult->num_rows > 0) {
        $latestRow = $latestResult->fetch_assoc();
        echo "<h2>Latest Record:</h2>";
        echo "<pre>";
        print_r($latestRow);
        echo "</pre>";
    } else {
        echo "<p style='color:orange'>No records found in the table.</p>";
    }
} else {
    echo "<p style='color:red'>Table 'power_data' does not exist!</p>";
}

// Close connection
$conn->close();
?>
