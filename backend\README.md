# ESP32 Three-Phase Electrical Parameters Logger Backend

This backend system receives three-phase electrical parameters data from an ESP32 board via WiFi and stores it in a MySQL database.

## Setup Instructions

1. Make sure you have the following installed:
   - PHP 7.0 or higher
   - MySQL/MariaDB
   - Web server (Apache/Nginx)

2. Database Setup:
   - Import the `database.sql` file into your MySQL server
   - Update the database credentials in `receive_data.php`:
     - $servername
     - $username
     - $password
     - $dbname

3. Place the PHP files in your web server's document root directory

## ESP32 Configuration

To send three-phase electrical parameters from your ESP32, use the following code structure:

```cpp
#include <WiFi.h>
#include <HTTPClient.h>

const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";
const char* serverUrl = "http://YOUR_SERVER_IP/backend/receive_data.php";

void setup() {
  Serial.begin(115200);
  WiFi.begin(ssid, password);
  
  while (WiFi.status() != WL_CONNECTED) {
    delay(1000);
    Serial.println("Connecting to WiFi...");
  }
}

void loop() {
  if (WiFi.status() == WL_CONNECTED) {
    HTTPClient http;
    http.begin(serverUrl);
    http.addHeader("Content-Type", "application/json");
    
    // Create JSON data with three-phase electrical parameters
    String jsonData = "{\"voltage_1\":230.0,\"voltage_2\":230.0,\"voltage_3\":230.0,\"current_1\":5.0,\"current_2\":5.0,\"current_3\":5.0,\"pf_1\":0.95,\"pf_2\":0.95,\"pf_3\":0.95,\"kva_1\":1150.0,\"kva_2\":1150.0,\"kva_3\":1150.0,\"total_kva\":3450.0,\"total_kw\":3277.5,\"total_kvar\":1076.25,\"frequency\":50.0}";
    
    int httpResponseCode = http.POST(jsonData);
    
    if (httpResponseCode > 0) {
      String response = http.getString();
      Serial.println(response);
    }
    
    http.end();
  }
  
  delay(5000); // Send data every 5 seconds
}
```

## API Endpoint

- URL: `http://YOUR_SERVER_IP/backend/receive_data.php`
- Method: POST
- Content-Type: application/json
- Data Format:
```json
{
    "voltage_1": 230.0,    // Phase 1 Voltage (V)
    "voltage_2": 230.0,    // Phase 2 Voltage (V)
    "voltage_3": 230.0,    // Phase 3 Voltage (V)
    
    "current_1": 5.0,      // Phase 1 Current (A)
    "current_2": 5.0,      // Phase 2 Current (A)
    "current_3": 5.0,      // Phase 3 Current (A)
    
    "pf_1": 0.95,         // Phase 1 Power Factor
    "pf_2": 0.95,         // Phase 2 Power Factor
    "pf_3": 0.95,         // Phase 3 Power Factor
    
    "kva_1": 1150.0,      // Phase 1 kVA
    "kva_2": 1150.0,      // Phase 2 kVA
    "kva_3": 1150.0,      // Phase 3 kVA
    
    "total_kva": 3450.0,  // Total kVA
    "total_kw": 3277.5,   // Total kW
    "total_kvar": 1076.25,// Total kVAR
    
    "frequency": 50.0     // System Frequency (Hz)
}
```

## Database Structure

The system stores the following three-phase electrical parameters:
- Phase Voltages (V1, V2, V3)
- Phase Currents (I1, I2, I3)
- Phase Power Factors (PF1, PF2, PF3)
- Phase Apparent Power (kVA1, kVA2, kVA3)
- Total Power (kVA, kW, kVAR)
- System Frequency (Hz)
- Timestamp

## Security Notes

1. Change the default database credentials
2. Consider implementing authentication for the API endpoint
3. Use HTTPS for production deployment
4. Implement rate limiting to prevent abuse 