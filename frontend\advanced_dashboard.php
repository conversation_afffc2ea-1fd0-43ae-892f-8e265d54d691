<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>Advanced Power Monitoring Dashboard</title>
    
    <!-- Chart.js and plugins -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.1/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0/dist/chartjs-adapter-luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-crosshair@1.2.0/dist/chartjs-plugin-crosshair.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-datalabels@2.0.0"></script>
    
    <!-- Grid.js for data tables -->
    <script src="https://cdn.jsdelivr.net/npm/gridjs/dist/gridjs.umd.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/gridjs/dist/theme/mermaid.min.css" rel="stylesheet" />
    
    <!-- Custom scripts -->
    <script src="chart-config.js?v=<?php echo time(); ?>"></script>
    <script src="sse-client.js?v=<?php echo time(); ?>"></script>
    <script src="advanced-dashboard.js?v=<?php echo time(); ?>"></script>
    
    <!-- Advanced styling -->
    <link rel="stylesheet" href="advanced-dashboard.css?v=<?php echo time(); ?>">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Material Icons -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Round" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet">
</head>
<body class="light-theme">
    <div class="dashboard-container">
        <!-- Sidebar Navigation -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <span class="material-icons-round">electric_meter</span>
                <h2>Online Data Logger</h2>
            </div>
            
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="#" id="dashboardLink">
                            <span class="material-icons-round">dashboard</span>
                            <span>Dashboard</span>
                        </a>
                    </li>
                    <li>
                        <a href="history.php" id="historyLink">
                            <span class="material-icons-round">history</span>
                            <span>Historical Data</span>
                        </a>
                    </li>
                    <li>
                        <a href="old_graphs.php" id="oldGraphsLink">
                            <span class="material-icons-round">show_chart</span>
                            <span>Old Graphs</span>
                        </a>
                    </li>
                    <li>
                        <a href="#" id="settingsLink">
                            <span class="material-icons-round">settings</span>
                            <span>Settings</span>
                        </a>
                    </li>
                </ul>
            </nav>
            
            <div class="sidebar-footer">
                <div class="system-status">
                    <span class="status-indicator online"></span>
                    <span>System Online</span>
                </div>
            </div>
        </aside>
        
        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <div class="page-title">
                    <h1>Advanced Power Monitoring Dashboard</h1>
                    <p class="last-updated">Last updated: <span id="lastUpdatedTime">Loading...</span></p>
                </div>
                <div class="top-bar-controls">
                    <div class="status-section">
                        <div class="connection-status">
                            <div class="status-indicator connecting" id="connectionIndicator"></div>
                            <span class="status-text" id="connectionText">Connecting...</span>
                        </div>
                        <div class="data-freshness fresh" id="dataFreshness">
                            <div class="status-dot"></div>
                            <span>Live Data</span>
                        </div>
                    </div>

                    <div class="performance-metrics">
                        <div class="metric">
                            <span class="metric-label">Latency</span>
                            <span class="metric-value" id="dataLatency">-- ms</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Updates</span>
                            <span class="metric-value" id="dataCount">0</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Uptime</span>
                            <span class="metric-value" id="connectionDuration">0:00</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Quality</span>
                            <span class="metric-value" id="dataQualityTop">---%</span>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button class="control-button primary" id="refreshBtn">
                            <span class="material-icons-round">refresh</span>
                            Refresh
                        </button>
                        <button class="control-button" id="resetMetricsTop">
                            <span class="material-icons-round">restart_alt</span>
                            Reset
                        </button>
                    </div>
                </div>
            </header>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <span class="material-icons-round">menu</span>
            </button>
            
            <!-- Main Dashboard Grid -->
            <section class="dashboard-grid">
                <!-- Voltage Graph -->
                <div class="graph-widget" id="voltageWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">bolt</span>
                            <h3>Phase Voltages</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" id="voltagePause" onclick="togglePause('voltage')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                        </div>
                    </div>

                    <div class="widget-values-section">
                        <div class="instant-values" id="voltageValues">
                            <div class="instant-values-content">
                                <div class="value-row">
                                    <span class="phase-label">L1:</span>
                                    <span class="phase-value" id="voltage1Value">-- V</span>
                                </div>
                                <div class="value-row">
                                    <span class="phase-label">L2:</span>
                                    <span class="phase-value" id="voltage2Value">-- V</span>
                                </div>
                                <div class="value-row">
                                    <span class="phase-label">L3:</span>
                                    <span class="phase-value" id="voltage3Value">-- V</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="widget-content">
                        <canvas id="voltageChart"></canvas>
                    </div>
                </div>
                
                <!-- Current Graph -->
                <div class="graph-widget" id="currentWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">electric_bolt</span>
                            <h3>Phase Currents</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" id="currentPause" onclick="togglePause('current')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                        </div>
                    </div>

                    <div class="widget-values-section">
                        <div class="instant-values" id="currentValues">
                            <div class="instant-values-content">
                                <div class="value-row">
                                    <span class="phase-label">L1:</span>
                                    <span class="phase-value" id="current1Value">-- A</span>
                                </div>
                                <div class="value-row">
                                    <span class="phase-label">L2:</span>
                                    <span class="phase-value" id="current2Value">-- A</span>
                                </div>
                                <div class="value-row">
                                    <span class="phase-label">L3:</span>
                                    <span class="phase-value" id="current3Value">-- A</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="widget-content">
                        <canvas id="currentChart"></canvas>
                    </div>
                </div>
                
                <!-- KVA Graph -->
                <div class="graph-widget" id="kvaWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">electric_meter</span>
                            <h3>KVA Values</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" id="kvaPause" onclick="togglePause('kva')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                        </div>
                    </div>

                    <div class="widget-values-section">
                        <div class="instant-values" id="kvaValues">
                            <div class="instant-values-content" id="kvaValuesContent">
                                <div class="value-row">
                                    <span class="phase-label">L1:</span>
                                    <span class="phase-value">-- kVA</span>
                                </div>
                                <div class="value-row">
                                    <span class="phase-label">L2:</span>
                                    <span class="phase-value">-- kVA</span>
                                </div>
                                <div class="value-row">
                                    <span class="phase-label">L3:</span>
                                    <span class="phase-value">-- kVA</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="widget-content">
                        <canvas id="kvaChart"></canvas>
                    </div>
                </div>
                
                <!-- Frequency Graph -->
                <div class="graph-widget" id="frequencyWidget">
                    <div class="widget-header">
                        <div class="widget-title">
                            <span class="material-icons-round">speed</span>
                            <h3>Frequency</h3>
                        </div>
                        <div class="widget-controls">
                            <button class="widget-control" id="frequencyPause" onclick="togglePause('frequency')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                        </div>
                    </div>
                    <div class="instant-values" id="frequencyValue">
                        <div class="instant-values-header">
                            <div class="instant-values-title">
                                <span class="material-icons-round">waves</span>
                                <h4>Frequency Value</h4>
                            </div>
                            <button class="instant-values-control" id="frequencyValuesPause" onclick="toggleValuesPause('frequency')" data-paused="false">
                                <span class="material-icons-round">pause</span>
                            </button>
                        </div>
                        <div class="instant-values-content" id="frequencyValueContent">
                            <div class="value-row">
                                <span class="phase-label">Frequency:</span>
                                <span class="phase-value">-- Hz</span>
                            </div>
                        </div>
                    </div>
                    <div class="widget-content">
                        <canvas id="frequencyChart"></canvas>
                    </div>
                </div>


            </section>
        </main>
    </div>
    
    <!-- Modals -->
    <div class="modal" id="settingsModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Dashboard Settings</h2>
                <button class="close-modal">
                    <span class="material-icons-round">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h3>Display Settings</h3>
                    <div class="setting-item">
                        <label for="refreshRate">Data Refresh Rate (seconds)</label>
                        <input type="number" id="refreshRate" min="1" max="60" value="2">
                    </div>
                    <div class="setting-item">
                        <label for="timeWindow">Default Time Window (minutes)</label>
                        <input type="number" id="timeWindow" min="1" max="60" value="1">
                    </div>
                    <div class="setting-item">
                        <label for="decimalPlaces">Decimal Places</label>
                        <input type="number" id="decimalPlaces" min="0" max="5" value="3">
                    </div>
                </div>
                
                <div class="settings-section">
                    <h3>Chart Settings</h3>
                    <div class="setting-item">
                        <label for="chartTheme">Chart Theme</label>
                        <select id="chartTheme">
                            <option value="professional">Professional</option>
                            <option value="material">Material</option>
                            <option value="pastel">Pastel</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="lineThickness">Line Thickness</label>
                        <input type="range" id="lineThickness" min="0.5" max="3" step="0.5" value="0.5">
                    </div>
                    <div class="setting-item checkbox">
                        <input type="checkbox" id="showGridLines" checked>
                        <label for="showGridLines">Show Grid Lines</label>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h3>Alert Settings</h3>
                    <div class="setting-item">
                        <label for="voltageAlertHigh">Voltage High Alert (V)</label>
                        <input type="number" id="voltageAlertHigh" value="450">
                    </div>
                    <div class="setting-item">
                        <label for="voltageAlertLow">Voltage Low Alert (V)</label>
                        <input type="number" id="voltageAlertLow" value="350">
                    </div>
                    <div class="setting-item">
                        <label for="frequencyAlertHigh">Frequency High Alert (Hz)</label>
                        <input type="number" id="frequencyAlertHigh" value="55">
                    </div>
                    <div class="setting-item">
                        <label for="frequencyAlertLow">Frequency Low Alert (Hz)</label>
                        <input type="number" id="frequencyAlertLow" value="45">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-button secondary" id="resetSettings">Reset to Defaults</button>
                <button class="modal-button primary" id="saveSettings">Save Settings</button>
            </div>
        </div>
    </div>
    
    <div class="modal" id="exportModal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Export Data</h2>
                <button class="close-modal">
                    <span class="material-icons-round">close</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="export-options">
                    <div class="export-option">
                        <input type="radio" name="exportType" id="exportCSV" value="csv" checked>
                        <label for="exportCSV">
                            <span class="material-icons-round">description</span>
                            <div>
                                <h4>CSV File</h4>
                                <p>Export data as comma-separated values</p>
                            </div>
                        </label>
                    </div>
                    
                    <div class="export-option">
                        <input type="radio" name="exportType" id="exportJSON" value="json">
                        <label for="exportJSON">
                            <span class="material-icons-round">data_object</span>
                            <div>
                                <h4>JSON File</h4>
                                <p>Export data in JSON format</p>
                            </div>
                        </label>
                    </div>
                    
                    <div class="export-option">
                        <input type="radio" name="exportType" id="exportPDF" value="pdf">
                        <label for="exportPDF">
                            <span class="material-icons-round">picture_as_pdf</span>
                            <div>
                                <h4>PDF Report</h4>
                                <p>Export data as a PDF report</p>
                            </div>
                        </label>
                    </div>
                </div>
                
                <div class="export-settings">
                    <h3>Export Settings</h3>
                    <div class="setting-item">
                        <label for="exportTimeRange">Time Range</label>
                        <select id="exportTimeRange">
                            <option value="visible">Currently Visible Data</option>
                            <option value="hour">Last Hour</option>
                            <option value="day">Last 24 Hours</option>
                            <option value="week">Last Week</option>
                            <option value="custom">Custom Range</option>
                        </select>
                    </div>
                    
                    <div class="date-range" id="customDateRange" style="display: none;">
                        <div class="setting-item">
                            <label for="exportStartDate">Start Date</label>
                            <input type="datetime-local" id="exportStartDate">
                        </div>
                        <div class="setting-item">
                            <label for="exportEndDate">End Date</label>
                            <input type="datetime-local" id="exportEndDate">
                        </div>
                    </div>
                    
                    <div class="setting-item checkbox">
                        <input type="checkbox" id="includeAllParameters" checked>
                        <label for="includeAllParameters">Include All Parameters</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="modal-button secondary" id="cancelExport">Cancel</button>
                <button class="modal-button primary" id="confirmExport">Export Data</button>
            </div>
        </div>
    </div>
    
    <!-- Fullscreen Widget Container -->
    <div class="fullscreen-widget" id="fullscreenWidget">
        <div class="fullscreen-header">
            <div class="fullscreen-title">
                <span class="material-icons-round" id="fullscreenIcon"></span>
                <h3 id="fullscreenTitle"></h3>
            </div>
            <div class="fullscreen-controls">
                <button class="fullscreen-control" id="fullscreenClose">
                    <span class="material-icons-round">close</span>
                </button>
            </div>
        </div>
        <div class="fullscreen-content" id="fullscreenContent"></div>
    </div>
    
    <!-- Notification System -->
    <div class="notification-container" id="notificationContainer"></div>

    <!-- Status Indicator -->
    <div id="statusIndicator"></div>

    <script>
        // Global SSE client instance
        let sseClient = null;

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Advanced Dashboard initializing with real-time SSE...');

            // Initialize mobile menu
            initializeMobileMenu();



            // Initialize refresh button
            initializeRefreshButton();

            // Initialize charts
            if (typeof window.initCharts === 'function') {
                window.initCharts();
            }

            // Initialize SSE client for real-time data
            initializeSSEClient();

            // Fallback: Also initialize polling as backup (with longer interval)
            if (typeof refreshDashboard === 'function') {
                // Initial load
                refreshDashboard();
                // Backup polling every 10 seconds (SSE should handle real-time updates)
                setInterval(refreshDashboard, 10000);
            }
        });

        function initializeSSEClient() {
            if (typeof SSEDataClient !== 'undefined') {
                sseClient = new SSEDataClient({
                    url: '../backend/sse_data_stream.php',
                    maxReconnectAttempts: 10,
                    reconnectDelay: 2000,
                    onData: function(data) {
                        // Handle real-time data updates
                        console.log('Real-time data received:', data);

                        // Update summary cards and individual values
                        if (typeof updateSummaryCards === 'function') {
                            updateSummaryCards(data);
                        }

                        // Update charts
                        if (typeof updateCharts === 'function') {
                            updateCharts(data);
                        }

                        // Show notification for first data
                        if (sseClient.getPerformanceMetrics().dataCount === 1) {
                            if (typeof showNotification === 'function') {
                                showNotification('Real-time data streaming active', 'success');
                            }
                        }
                    },
                    onConnected: function() {
                        console.log('SSE connected successfully');
                        if (typeof showNotification === 'function') {
                            showNotification('Real-time connection established', 'success');
                        }
                    },
                    onError: function(error) {
                        console.error('SSE connection error:', error);
                        if (typeof showNotification === 'function') {
                            showNotification('Real-time connection error, using fallback', 'warning');
                        }
                    },
                    onDisconnected: function() {
                        console.log('SSE disconnected');
                        if (typeof showNotification === 'function') {
                            showNotification('Real-time connection lost, using fallback', 'warning');
                        }
                    }
                });
            } else {
                console.warn('SSEDataClient not available, using polling fallback');
                if (typeof showNotification === 'function') {
                    showNotification('Using polling mode (SSE not available)', 'info');
                }
            }
        }

        function initializeMobileMenu() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const sidebar = document.querySelector('.sidebar');

            if (mobileMenuToggle && sidebar) {
                mobileMenuToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('mobile-open');
                });

                // Close menu when clicking outside
                document.addEventListener('click', function(e) {
                    if (!sidebar.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                        sidebar.classList.remove('mobile-open');
                    }
                });
            }
        }



        function initializeRefreshButton() {
            const refreshBtn = document.getElementById('refreshBtn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    if (typeof refreshDashboard === 'function') {
                        refreshDashboard();
                        if (typeof showNotification === 'function') {
                            showNotification('Dashboard refreshed', 'success');
                        }
                    }
                });
            }

            // Initialize reset metrics buttons
            const resetMetricsBtn = document.getElementById('resetMetrics');
            const resetMetricsTopBtn = document.getElementById('resetMetricsTop');

            function resetMetrics() {
                if (sseClient) {
                    // Reset performance metrics
                    sseClient.performanceMetrics = {
                        connectionStartTime: Date.now(),
                        lastDataReceived: null,
                        dataCount: 0,
                        averageLatency: 0,
                        connectionDuration: 0
                    };

                    // Update display
                    sseClient.updatePerformanceDisplay();

                    if (typeof showNotification === 'function') {
                        showNotification('Performance metrics reset', 'info');
                    }
                }
            }

            if (resetMetricsBtn) {
                resetMetricsBtn.addEventListener('click', resetMetrics);
            }

            if (resetMetricsTopBtn) {
                resetMetricsTopBtn.addEventListener('click', resetMetrics);
            }
        }
    </script>

</body>
</html>
