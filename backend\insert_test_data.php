<?php
// Include database connection
require_once 'db_connect.php';

// Set headers for plain text response
header('Content-Type: text/plain');

// Enable error reporting for debugging
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "Test Data Insertion Script\n";
echo "=========================\n\n";

// Check if the electrical_data table exists
$tableCheckQuery = "SHOW TABLES LIKE 'electrical_data'";
$tableResult = $conn->query($tableCheckQuery);

if ($tableResult->num_rows == 0) {
    echo "Table 'electrical_data' does not exist. Creating table...\n";
    
    // Create the table
    $createTableSql = "CREATE TABLE `electrical_data` (
        `id` int(11) NOT NULL AUTO_INCREMENT,
        `timestamp` datetime NOT NULL,
        `voltage_1` float DEFAULT NULL,
        `voltage_2` float DEFAULT NULL,
        `voltage_3` float DEFAULT NULL,
        `current_1` float DEFAULT NULL,
        `current_2` float DEFAULT NULL,
        `current_3` float DEFAULT NULL,
        `pf_1` float DEFAULT NULL,
        `pf_2` float DEFAULT NULL,
        `pf_3` float DEFAULT NULL,
        `kva_1` float DEFAULT NULL,
        `kva_2` float DEFAULT NULL,
        `kva_3` float DEFAULT NULL,
        `total_kw` float DEFAULT NULL,
        `total_kva` float DEFAULT NULL,
        `total_kvar` float DEFAULT NULL,
        `frequency` float DEFAULT NULL,
        PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;";
    
    if ($conn->query($createTableSql) === TRUE) {
        echo "Table 'electrical_data' created successfully.\n\n";
    } else {
        echo "Error creating table: " . $conn->error . "\n\n";
        exit;
    }
} else {
    echo "Table 'electrical_data' already exists.\n\n";
}

// Insert test data
echo "Inserting test data...\n";

// Prepare the insert statement
$insertSql = "INSERT INTO electrical_data (
    timestamp, 
    voltage_1, voltage_2, voltage_3, 
    current_1, current_2, current_3, 
    pf_1, pf_2, pf_3, 
    kva_1, kva_2, kva_3, 
    total_kw, total_kva, total_kvar, 
    frequency
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$stmt = $conn->prepare($insertSql);
$stmt->bind_param(
    "sdddddddddddddddd",
    $timestamp, 
    $voltage1, $voltage2, $voltage3, 
    $current1, $current2, $current3, 
    $pf1, $pf2, $pf3, 
    $kva1, $kva2, $kva3, 
    $totalKw, $totalKva, $totalKvar, 
    $frequency
);

// Generate test data for the last 24 hours
$startTime = strtotime('-24 hours');
$endTime = time();
$interval = 60; // 1 minute interval
$recordsInserted = 0;

for ($t = $startTime; $t <= $endTime; $t += $interval) {
    // Generate timestamp
    $timestamp = date('Y-m-d H:i:s', $t);
    
    // Generate random values with some variation but realistic
    $baseVoltage = 220 + (rand(-5, 5) / 10);
    $voltage1 = $baseVoltage + (rand(-20, 20) / 10);
    $voltage2 = $baseVoltage + (rand(-20, 20) / 10);
    $voltage3 = $baseVoltage + (rand(-20, 20) / 10);
    
    $baseCurrent = 5 + (rand(-10, 10) / 10);
    $current1 = $baseCurrent + (rand(-10, 10) / 10);
    $current2 = $baseCurrent + (rand(-10, 10) / 10);
    $current3 = $baseCurrent + (rand(-10, 10) / 10);
    
    $basePf = 0.85 + (rand(-10, 10) / 100);
    $pf1 = min(1, max(0, $basePf + (rand(-5, 5) / 100)));
    $pf2 = min(1, max(0, $basePf + (rand(-5, 5) / 100)));
    $pf3 = min(1, max(0, $basePf + (rand(-5, 5) / 100)));
    
    $kva1 = $voltage1 * $current1 / 1000;
    $kva2 = $voltage2 * $current2 / 1000;
    $kva3 = $voltage3 * $current3 / 1000;
    
    $totalKva = $kva1 + $kva2 + $kva3;
    $totalKw = $totalKva * $basePf;
    $totalKvar = sqrt(pow($totalKva, 2) - pow($totalKw, 2));
    
    $frequency = 50 + (rand(-20, 20) / 100);
    
    // Execute the insert statement
    $stmt->execute();
    $recordsInserted++;
    
    // Show progress every 100 records
    if ($recordsInserted % 100 == 0) {
        echo "Inserted $recordsInserted records...\n";
    }
}

echo "\nInserted a total of $recordsInserted test records.\n";
echo "Test data insertion complete.\n";

// Close connection
$stmt->close();
$conn->close();
?>
