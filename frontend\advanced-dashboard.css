/* Advanced Dashboard Styling */
:root {
    /* Professional color palette */
    --primary-color: #1a73e8;
    --primary-dark: #0d47a1;
    --primary-light: #4285f4;
    --secondary-color: #34a853;
    --accent-color: #ea4335;
    --warning-color: #fbbc04;
    --success-color: #0f9d58;
    --text-color: #202124;
    --text-secondary: #5f6368;
    --background-color: #f8f9fa;
    --card-color: #ffffff;
    --border-color: #dadce0;
    --hover-color: #f1f3f4;
    --shadow-sm: 0 1px 2px 0 rgba(60, 64, 67, 0.1), 0 1px 3px 1px rgba(60, 64, 67, 0.08);
    --shadow-md: 0 2px 6px 2px rgba(60, 64, 67, 0.15);
    --shadow-lg: 0 4px 8px 3px rgba(60, 64, 67, 0.15), 0 1px 3px rgba(60, 64, 67, 0.3);
    --font-family: 'Inter', 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    --border-radius: 8px;
    --transition-speed: 0.2s;
    

}



* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: var(--font-family);
}

body {
    background-color: var(--background-color);
    color: var(--text-color);
    line-height: 1.6;
    font-size: 14px;
    font-weight: 400;
    letter-spacing: 0.01em;
    transition: background-color var(--transition-speed) ease;
    overflow-x: hidden;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .main-content {
        margin-left: 0;
        padding: 16px;
    }

    .sidebar {
        transform: translateX(-100%);
        transition: transform var(--transition-speed) ease;
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .mobile-menu-toggle {
        display: block;
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 1001;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        box-shadow: var(--shadow-md);
    }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .graph-widget {
        border-radius: 12px;
    }

    .widget-header {
        padding: 16px 20px 12px;
    }

    .widget-content {
        padding: 16px 20px 20px;
    }

    .widget-title h3 {
        font-size: 16px;
    }

    .summary-cards {
        grid-template-columns: 1fr;
        gap: 16px;
        margin-bottom: 20px;
    }

    .summary-card {
        padding: 20px;
    }
}

@media (max-width: 480px) {
    .main-content {
        padding: 12px;
    }

    .top-bar {
        padding: 12px 16px;
        margin-bottom: 16px;
    }

    .widget-header {
        padding: 12px 16px 8px;
    }

    .widget-content {
        padding: 12px 16px 16px;
    }

    .instant-values {
        font-size: 12px;
        padding: 8px;
    }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--background-color) 0%, rgba(248,249,250,0.8) 100%);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 24px;
    margin-top: 20px;
}

/* Graph Widgets */
.graph-widget {
    background: var(--card-color);
    border-radius: 16px;
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
    overflow: hidden;
    transition: all var(--transition-speed) ease;
    position: relative;
}

.graph-widget::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
}

.graph-widget:hover::before {
    opacity: 1;
}

.graph-widget:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.widget-header {
    padding: 20px 24px 16px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,249,250,0.9) 100%);
}

.widget-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.widget-title h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.widget-title .material-icons-round {
    font-size: 24px;
    color: var(--primary-color);
}

.widget-controls {
    display: flex;
    gap: 8px;
}

.widget-control {
    background: var(--hover-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 8px;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.widget-control:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.widget-control .material-icons-round {
    font-size: 18px;
}

.widget-content {
    padding: 20px 24px 24px;
    position: relative;
}

.instant-values {
    margin-top: 16px;
    padding: 16px;
    background: linear-gradient(135deg, var(--hover-color) 0%, rgba(248,249,250,0.8) 100%);
    border-radius: 12px;
    font-family: 'Inter', sans-serif;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    border: 1px solid var(--border-color);
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.value-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--border-color);
}

.value-row:last-child {
    border-bottom: none;
}

.phase-label {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 13px;
}

.phase-value {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 16px;
    font-family: 'Courier New', monospace;
}

/* Performance Metrics */
.performance-metrics {
    display: flex;
    gap: 20px;
    align-items: center;
}

.metric {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.metric-label {
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}

.metric-value {
    font-size: 14px;
    font-weight: 700;
    color: var(--primary-color);
    font-family: 'Courier New', monospace;
}

.top-bar-controls {
    display: flex;
    align-items: center;
    gap: 24px;
}

.status-section {
    display: flex;
    align-items: center;
    gap: 16px;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(76, 175, 80, 0.1);
    border-radius: 20px;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.connection-status .status-text {
    font-size: 14px;
    font-weight: 500;
    color: var(--success-color);
}

.control-buttons {
    display: flex;
    gap: 8px;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background-color: var(--card-color);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    position: fixed;
    height: 100vh;
    z-index: 100;
    transition: all var(--transition-speed) ease;
}

.sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-header span {
    color: var(--primary-color);
    font-size: 24px;
}

.sidebar-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 4px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 0 24px 24px 0;
    transition: all var(--transition-speed) ease;
    gap: 12px;
    margin-right: 12px;
}

.sidebar-nav a:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
}

.sidebar-nav li.active a {
    background-color: #e8f0fe;
    color: var(--primary-color);
    font-weight: 500;
}



.sidebar-nav span.material-icons-round {
    font-size: 20px;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.theme-toggle {
    display: flex;
    align-items: center;
    gap: 12px;
    background: none;
    border: none;
    color: var(--text-secondary);
    padding: 10px;
    border-radius: 24px;
    cursor: pointer;
    width: 100%;
    transition: all var(--transition-speed) ease;
    margin-bottom: 16px;
}

.theme-toggle:hover {
    background-color: var(--hover-color);
}

.system-status {
    display: flex;
    align-items: center;
    gap: 8px;
    color: var(--text-secondary);
    font-size: 13px;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--success-color);
    position: relative;
    display: inline-block;
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 50%;
    background: inherit;
    opacity: 0.3;
    animation: pulse 2s infinite;
}

.status-indicator.online {
    background-color: var(--success-color);
}

.status-indicator.offline {
    background-color: var(--accent-color);
}

.status-indicator.warning {
    background-color: var(--warning-color);
}

.status-indicator.connecting {
    background-color: var(--warning-color);
    animation: blink 1s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.1;
    }
    100% {
        transform: scale(1);
        opacity: 0.3;
    }
}

@keyframes blink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

/* Real-time Data Indicators */
.data-freshness {
    position: absolute;
    top: 12px;
    right: 12px;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-secondary);
    background: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 12px;
    border: 1px solid var(--border-color);
}

.data-freshness .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: var(--success-color);
}

.data-freshness.stale .status-dot {
    background: var(--warning-color);
}

.data-freshness.error .status-dot {
    background: var(--accent-color);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 260px;
    padding: 20px;
    transition: all var(--transition-speed) ease;
}

/* Top Bar */
.top-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 20px;
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
}

.page-title h1 {
    font-size: 20px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.last-updated {
    font-size: 12px;
    color: var(--text-secondary);
}

.top-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.time-navigation {
    display: flex;
    align-items: center;
    background-color: var(--hover-color);
    border-radius: 24px;
    padding: 4px;
    margin-right: 8px;
}

#timeDisplay {
    padding: 6px 12px;
    font-weight: 500;
    color: var(--text-color);
    background-color: var(--card-color);
    border-radius: 20px;
    box-shadow: var(--shadow-sm);
    margin: 0 4px;
    font-size: 13px;
}

.icon-button {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.icon-button:hover {
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--primary-color);
}

.control-button {
    padding: 8px 16px;
    border: none;
    border-radius: 24px;
    background-color: var(--hover-color);
    color: var(--text-color);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
    font-weight: 500;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.control-button:hover {
    background-color: rgba(0, 0, 0, 0.08);
}

.control-button.primary {
    background-color: var(--primary-color);
    color: white;
}

.control-button.primary:hover {
    background-color: var(--primary-dark);
}

.control-button.secondary {
    background-color: var(--secondary-color);
    color: white;
}

.control-button.secondary:hover {
    background-color: #2d9548;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: linear-gradient(135deg, var(--card-color) 0%, rgba(255,255,255,0.95) 100%);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    padding: 24px;
    display: flex;
    align-items: center;
    transition: all var(--transition-speed) ease;
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.summary-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity var(--transition-speed) ease;
}

.summary-card:hover::before {
    opacity: 1;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.summary-card:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.card-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16px;
}

.summary-card.voltage .card-icon {
    background-color: rgba(26, 115, 232, 0.1);
    color: var(--primary-color);
}

.summary-card.current .card-icon {
    background-color: rgba(234, 67, 53, 0.1);
    color: var(--accent-color);
}

.summary-card.power .card-icon {
    background-color: rgba(52, 168, 83, 0.1);
    color: var(--secondary-color);
}

.summary-card.frequency .card-icon {
    background-color: rgba(251, 188, 4, 0.1);
    color: var(--warning-color);
}

.card-icon span {
    font-size: 24px;
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 14px;
    font-weight: 500;
    color: var(--text-secondary);
    margin-bottom: 4px;
}

.card-value {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.trend-indicator {
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 500;
}

.trend-indicator.up {
    color: var(--secondary-color);
}

.trend-indicator.down {
    color: var(--accent-color);
}

.trend-indicator.stable {
    color: var(--warning-color);
}

.trend-indicator span.material-icons-round {
    font-size: 16px;
    margin-right: 4px;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
    gap: 20px;
}

/* Graph Widget */
.graph-widget {
    background-color: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
    transition: all var(--transition-speed) ease;
    height: 400px;
    border: 1px solid var(--border-color);
    position: relative;
}

.graph-widget:hover {
    box-shadow: var(--shadow-md);
}

.widget-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
}

.widget-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.widget-title span {
    color: var(--primary-color);
    font-size: 20px;
}

.widget-title h3 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
}

.widget-controls {
    display: flex;
    gap: 4px;
}

.widget-control {
    width: 28px;
    height: 28px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.widget-control:hover {
    background-color: var(--hover-color);
    color: var(--primary-color);
}

.widget-control[data-paused="true"] {
    color: var(--accent-color);
    background-color: rgba(234, 67, 53, 0.1);
}

.widget-control[data-auto-scroll="true"] {
    color: var(--primary-color);
    background-color: rgba(26, 115, 232, 0.1);
}

.widget-content {
    height: calc(100% - 53px);
    position: relative;
}

canvas {
    width: 100% !important;
    height: 100% !important;
}

/* Instant Values */
.instant-values {
    position: relative;
    margin-top: 16px;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 0;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    font-size: 12px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    backdrop-filter: blur(8px);
}



.instant-values div {
    display: flex;
    justify-content: space-between;
    gap: 12px;
}

.instant-values span {
    font-weight: 600;
    color: var(--primary-color);
}

/* Status Indicator */
#statusIndicator {
    position: fixed;
    bottom: 20px;
    right: 20px;
    padding: 10px 16px;
    background-color: var(--card-color);
    color: var(--text-color);
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    z-index: 1000;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.3s ease;
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: none;
    border: 1px solid var(--border-color);
}

#statusIndicator.error {
    background-color: #fdeded;
    color: var(--accent-color);
    border-color: #f6aea9;
}

#statusIndicator.warning {
    background-color: #fef7e0;
    color: var(--warning-color);
    border-color: #feefc3;
}

#statusIndicator.success {
    background-color: #e6f4ea;
    color: var(--success-color);
    border-color: #ceead6;
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 350px;
}

.notification {
    background-color: var(--card-color);
    border-radius: 8px;
    box-shadow: var(--shadow-md);
    padding: 12px 16px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: slideIn 0.3s ease forwards;
    border-left: 4px solid var(--primary-color);
    max-width: 100%;
}

.notification.info {
    border-left-color: var(--primary-color);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.warning {
    border-left-color: var(--warning-color);
}

.notification.error {
    border-left-color: var(--accent-color);
}

.notification-icon {
    color: var(--primary-color);
    font-size: 20px;
}

.notification.success .notification-icon {
    color: var(--success-color);
}

.notification.warning .notification-icon {
    color: var(--warning-color);
}

.notification.error .notification-icon {
    color: var(--accent-color);
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    font-size: 14px;
    margin-bottom: 4px;
    color: var(--text-color);
}

.notification-message {
    font-size: 13px;
    color: var(--text-secondary);
}

.notification-close {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 18px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background-color: var(--card-color);
    border-radius: 12px;
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 600px;
    max-height: 90vh;
    overflow-y: auto;
    animation: modalIn 0.3s ease forwards;
}

.modal-header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    background-color: var(--card-color);
    z-index: 1;
}

.modal-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.close-modal {
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: 24px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    position: sticky;
    bottom: 0;
    background-color: var(--card-color);
    z-index: 1;
}

.modal-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: all var(--transition-speed) ease;
}

.modal-button.primary {
    background-color: var(--primary-color);
    color: white;
}

.modal-button.primary:hover {
    background-color: var(--primary-dark);
}

.modal-button.secondary {
    background-color: var(--hover-color);
    color: var(--text-color);
}

.modal-button.secondary:hover {
    background-color: rgba(0, 0, 0, 0.08);
}

@keyframes modalIn {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Settings */
.settings-section {
    margin-bottom: 24px;
}

.settings-section h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 16px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--border-color);
}

.setting-item {
    margin-bottom: 16px;
}

.setting-item label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;
}

.setting-item input[type="number"],
.setting-item input[type="text"],
.setting-item input[type="datetime-local"],
.setting-item select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--card-color);
    transition: all var(--transition-speed) ease;
}

.setting-item input[type="range"] {
    width: 100%;
    height: 6px;
    -webkit-appearance: none;
    background: var(--hover-color);
    border-radius: 3px;
    outline: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.setting-item input:focus,
.setting-item select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.setting-item.checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
}

.setting-item.checkbox input {
    width: 16px;
    height: 16px;
}

.setting-item.checkbox label {
    margin-bottom: 0;
}

/* Export Options */
.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.export-option {
    position: relative;
}

.export-option input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

.export-option label {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.export-option input[type="radio"]:checked + label {
    border-color: var(--primary-color);
    background-color: rgba(26, 115, 232, 0.05);
}

.export-option label span.material-icons-round {
    font-size: 24px;
    color: var(--primary-color);
}

.export-option label h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
}

.export-option label p {
    font-size: 12px;
    color: var(--text-secondary);
}

.date-range {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

/* Fullscreen Widget */
.fullscreen-widget {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: var(--background-color);
    z-index: 1001;
    display: none;
    flex-direction: column;
}

.fullscreen-header {
    padding: 16px 24px;
    background-color: var(--card-color);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.fullscreen-title {
    display: flex;
    align-items: center;
    gap: 12px;
}

.fullscreen-title span {
    color: var(--primary-color);
    font-size: 24px;
}

.fullscreen-title h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color);
}

.fullscreen-controls {
    display: flex;
    gap: 8px;
}

.fullscreen-control {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all var(--transition-speed) ease;
}

.fullscreen-control:hover {
    background-color: var(--hover-color);
    color: var(--accent-color);
}

.fullscreen-content {
    flex: 1;
    padding: 0;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--background-color);
}

.fullscreen-content canvas {
    width: 98vw !important;
    height: 80vh !important;
    max-width: 100%;
    max-height: 100%;
    display: block;
    background: var(--card-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-lg);
}

@media (max-width: 768px) {
    .fullscreen-content canvas {
        width: 100vw !important;
        height: 60vh !important;
    }
}

/* Responsive Design */
@media (max-width: 1200px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .sidebar {
        width: 64px;
        overflow: hidden;
    }
    
    .sidebar-header h2,
    .sidebar-nav a span:not(.material-icons-round),
    .theme-toggle span:not(.material-icons-round),
    .system-status span:not(.status-indicator) {
        display: none;
    }
    
    .sidebar-header {
        justify-content: center;
        padding: 16px 0;
    }
    
    .sidebar-nav a {
        justify-content: center;
        padding: 12px 0;
        margin-right: 0;
    }
    
    .theme-toggle {
        justify-content: center;
        padding: 10px 0;
    }
    
    .system-status {
        justify-content: center;
    }
    
    .main-content {
        margin-left: 64px;
    }
}

@media (max-width: 768px) {
    .top-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 16px;
    }
    
    .top-controls {
        width: 100%;
        flex-wrap: wrap;
    }
    
    .summary-cards {
        grid-template-columns: 1fr;
    }
    
    .instant-values {
        position: static;
        margin-top: 16px;
        width: 100%;
    }
    
    .graph-widget {
        height: 300px;
    }
}

@media (max-width: 576px) {
    .main-content {
        padding: 10px;
    }
    
    .modal-content {
        width: 95%;
    }
    
    .date-range {
        grid-template-columns: 1fr;
    }
}

/* Enhanced Status indicators for real-time streaming */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
    margin-right: 8px;
}

.status-indicator.connected {
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.status-indicator.connecting {
    background: var(--warning-color);
    animation: blink 1s infinite;
}

.status-indicator.error {
    background: var(--error-color);
    animation: none;
}

.status-indicator.disconnected {
    background: #ccc;
    animation: none;
}

/* Data freshness indicators */
.data-freshness {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 12px;
    color: var(--text-secondary);
    margin-bottom: 8px;
    padding: 4px 8px;
    background: rgba(76, 175, 80, 0.1);
    border-radius: 12px;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.data-freshness.fresh {
    background: rgba(76, 175, 80, 0.1);
    border-color: rgba(76, 175, 80, 0.3);
    color: var(--success-color);
}

.data-freshness.error {
    background: rgba(244, 67, 54, 0.1);
    border-color: rgba(244, 67, 54, 0.3);
    color: var(--error-color);
}

.data-freshness .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    animation: pulse 2s infinite;
}

.data-freshness.error .status-dot {
    background: var(--error-color);
    animation: none;
}

/* Mobile menu toggle */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1001;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.mobile-menu-toggle:hover {
    background: var(--primary-dark);
}

@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0.3; }
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Mobile responsive enhancements */
@media (max-width: 768px) {
    .mobile-menu-toggle {
        display: block;
    }

    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .performance-metrics {
        flex-direction: column;
        gap: 8px;
    }

    .metric {
        flex-direction: row;
        justify-content: space-between;
        width: 100%;
    }

    .top-bar-controls {
        flex-direction: column;
        gap: 12px;
        width: 100%;
    }
}

/* Performance Monitoring Widget Styles */
.performance-widget {
    background: linear-gradient(135deg, var(--card-color) 0%, rgba(248,249,250,0.95) 100%);
    border: 1px solid var(--border-color);
}

.performance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-top: 16px;
}

.performance-metric {
    background: var(--background-color);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    transition: all var(--transition-speed) ease;
}

.performance-metric:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.metric-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin-bottom: 12px;
}

.metric-header .material-icons-round {
    color: var(--primary-color);
    font-size: 20px;
}

.metric-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-secondary);
}

.metric-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--text-color);
    margin-bottom: 4px;
    font-family: 'Courier New', monospace;
}

.metric-subtitle {
    font-size: 12px;
    color: var(--text-secondary);
    font-style: italic;
}

/* Connection status specific styling */
#connectionStatus {
    color: var(--success-color);
}

#connectionStatus.error {
    color: var(--error-color);
}

#connectionStatus.warning {
    color: var(--warning-color);
}

/* Instant Values Header Styling */
.instant-values-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-radius: 8px 8px 0 0;
    margin-bottom: 8px;
}

.instant-values-title {
    display: flex;
    align-items: center;
    gap: 8px;
}

.instant-values-title h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.instant-values-title .material-icons-round {
    font-size: 18px;
}

.instant-values-control {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    color: white;
    cursor: pointer;
    transition: background-color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.instant-values-control:hover {
    background: rgba(255, 255, 255, 0.3);
}

.instant-values-control[data-paused="true"] {
    background: rgba(255, 255, 255, 0.4);
}

.instant-values-control .material-icons-round {
    font-size: 16px;
}

.instant-values-content {
    padding: 0 12px 8px 12px;
}

/* Performance metric responsive design */
@media (max-width: 768px) {
    .performance-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }

    .performance-metric {
        padding: 12px;
    }

    .metric-value {
        font-size: 20px;
    }
}

@media (max-width: 480px) {
    .performance-grid {
        grid-template-columns: 1fr;
    }
}
