<?php
// Include database connection
require_once 'db_connect.php';

// Get parameters from request
$parameter = isset($_GET['parameter']) ? $_GET['parameter'] : '';
$start = isset($_GET['start']) ? $_GET['start'] : '';
$end = isset($_GET['end']) ? $_GET['end'] : '';

// Validate parameters
if (empty($parameter) || empty($start) || empty($end)) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Missing required parameters'
    ]);
    exit;
}

try {
    // Prepare SQL query to fetch data
    $sql = "SELECT * FROM electrical_data WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp ASC";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("ss", $start, $end);

    // Log the query for debugging
    error_log("CSV Export - Executing query: $sql with params: $start, $end");
    $stmt->execute();
    $result = $stmt->get_result();

    // Log the number of rows returned
    error_log("CSV Export - Number of rows returned: " . $result->num_rows);

    // Set headers for CSV download
    header('Content-Type: text/csv');
    $filename = ($parameter == 'all') ? 'all_electrical_data' : $parameter . '_data';
    $filename .= '_' . date('Y-m-d_His') . '.csv';
    header('Content-Disposition: attachment; filename="' . $filename . '"');

    // Create a file pointer connected to the output stream
    $output = fopen('php://output', 'w');

    // Define CSV headers based on parameter type
    $headers = ['Timestamp'];
    $dataFields = [];

    switch ($parameter) {
        case 'voltage':
            $headers = array_merge($headers, ['V12 (V)', 'V23 (V)', 'V31 (V)']);
            $dataFields = ['voltage_1', 'voltage_2', 'voltage_3'];
            break;
        case 'current':
            $headers = array_merge($headers, ['Ia (A)', 'Ib (A)', 'Ic (A)']);
            $dataFields = ['current_1', 'current_2', 'current_3'];
            break;
        case 'pf':
            $headers = array_merge($headers, ['PF1', 'PF2', 'PF3']);
            $dataFields = ['pf_1', 'pf_2', 'pf_3'];
            break;
        case 'kva':
            $headers = array_merge($headers, ['KVA1', 'KVA2', 'KVA3']);
            $dataFields = ['kva_1', 'kva_2', 'kva_3'];
            break;
        case 'power':
            $headers = array_merge($headers, ['Total KW', 'Total KVA', 'Total KVAR']);
            $dataFields = ['total_kw', 'total_kva', 'total_kvar'];
            break;
        case 'frequency':
            $headers = array_merge($headers, ['Frequency (Hz)']);
            $dataFields = ['frequency'];
            break;
        default:
            // If parameter is not recognized, include all fields (PF columns removed)
            $headers = [
                'Timestamp',
                'V12 (V)', 'V23 (V)', 'V31 (V)',
                'Ia (A)', 'Ib (A)', 'Ic (A)',
                'KVA1', 'KVA2', 'KVA3',
                'Total KW', 'Total KVA', 'Total KVAR',
                'Frequency (Hz)'
            ];
            $dataFields = [
                'voltage_1', 'voltage_2', 'voltage_3',
                'current_1', 'current_2', 'current_3',
                'kva_1', 'kva_2', 'kva_3',
                'total_kw', 'total_kva', 'total_kvar',
                'frequency'
            ];
    }

    // Output the column headings
    fputcsv($output, $headers);

    // Output each row of data
    while ($row = $result->fetch_assoc()) {
        $csvRow = [date('Y-m-d H:i:s', strtotime($row['timestamp']))]; // Format timestamp

        foreach ($dataFields as $field) {
            $csvRow[] = number_format((float)$row[$field], 3, '.', ''); // Format to 3 decimal places
        }

        fputcsv($output, $csvRow);
    }

    fclose($output);

} catch (Exception $e) {
    header('Content-Type: application/json');
    echo json_encode([
        'status' => 'error',
        'message' => 'Database error: ' . $e->getMessage()
    ]);
}

// Close connection
$conn->close();
?>
