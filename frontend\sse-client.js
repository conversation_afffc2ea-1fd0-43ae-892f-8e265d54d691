// SSE Client for Real-time Data Streaming
// Handles Server-Sent Events connection and data processing

class SSEDataClient {
    constructor(options = {}) {
        this.url = options.url || '../backend/sse_data_stream.php';
        this.eventSource = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = options.maxReconnectAttempts || 5;
        this.reconnectDelay = options.reconnectDelay || 2000;
        this.onDataCallback = options.onData || null;
        this.onErrorCallback = options.onError || null;
        this.onConnectedCallback = options.onConnected || null;
        this.onDisconnectedCallback = options.onDisconnected || null;
        
        // Performance tracking
        this.performanceMetrics = {
            connectionStartTime: null,
            lastDataReceived: null,
            dataCount: 0,
            averageLatency: 0,
            connectionDuration: 0
        };
        
        this.init();
    }
    
    init() {
        this.connect();
    }
    
    connect() {
        if (this.eventSource) {
            this.eventSource.close();
        }
        
        console.log('Connecting to SSE stream...');
        this.eventSource = new EventSource(this.url);
        this.performanceMetrics.connectionStartTime = Date.now();
        
        // Connection opened
        this.eventSource.onopen = (event) => {
            console.log('SSE connection opened');
            this.isConnected = true;
            this.reconnectAttempts = 0;
            this.updateConnectionStatus('connected');
            
            if (this.onConnectedCallback) {
                this.onConnectedCallback();
            }
        };
        
        // Handle different event types
        this.eventSource.addEventListener('connected', (event) => {
            const data = JSON.parse(event.data);
            console.log('SSE connected:', data);
            this.updateConnectionStatus('connected');
        });
        
        this.eventSource.addEventListener('data', (event) => {
            const data = JSON.parse(event.data);
            this.handleDataReceived(data);
        });
        
        this.eventSource.addEventListener('performance', (event) => {
            const perfData = JSON.parse(event.data);
            this.updatePerformanceMetrics(perfData);
        });
        
        this.eventSource.addEventListener('heartbeat', (event) => {
            const heartbeatData = JSON.parse(event.data);
            console.log('Heartbeat received:', heartbeatData);
            this.updateConnectionStatus('connected');
        });
        
        // Handle errors
        this.eventSource.onerror = (event) => {
            console.error('SSE connection error:', event);
            this.isConnected = false;
            this.updateConnectionStatus('error');
            
            if (this.onErrorCallback) {
                this.onErrorCallback(event);
            }
            
            // Attempt to reconnect
            this.attemptReconnect();
        };
    }
    
    handleDataReceived(data) {
        this.performanceMetrics.lastDataReceived = Date.now();
        this.performanceMetrics.dataCount++;
        
        // Calculate latency (approximate)
        const now = Date.now();
        const dataTimestamp = new Date(data.timestamp).getTime();
        const latency = now - dataTimestamp;
        
        // Update average latency
        this.performanceMetrics.averageLatency = 
            (this.performanceMetrics.averageLatency * (this.performanceMetrics.dataCount - 1) + latency) / 
            this.performanceMetrics.dataCount;
        
        console.log('Data received via SSE:', data);
        
        if (this.onDataCallback) {
            this.onDataCallback(data);
        }
        
        // Update UI performance indicators
        this.updatePerformanceDisplay();
    }
    
    updatePerformanceMetrics(perfData) {
        this.performanceMetrics.connectionDuration = perfData.connection_duration;
        console.log('Performance metrics updated:', perfData);
    }
    
    updatePerformanceDisplay() {
        // Update latency display (top bar)
        const latencyElement = document.getElementById('dataLatency');
        if (latencyElement) {
            latencyElement.textContent = `${Math.round(this.performanceMetrics.averageLatency)} ms`;
        }

        // Update data count (top bar)
        const dataCountElement = document.getElementById('dataCount');
        if (dataCountElement) {
            dataCountElement.textContent = this.performanceMetrics.dataCount.toString();
        }

        // Update connection duration (top bar)
        const durationElement = document.getElementById('connectionDuration');
        if (durationElement) {
            const duration = Math.floor(this.performanceMetrics.connectionDuration);
            const minutes = Math.floor(duration / 60);
            const seconds = duration % 60;
            durationElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }

        // Update performance widget metrics
        this.updatePerformanceWidget();
    }

    updatePerformanceWidget() {
        // Update connection status
        const connectionStatusElement = document.getElementById('connectionStatus');
        if (connectionStatusElement) {
            const statusText = this.isConnected ? 'Connected' : 'Disconnected';
            const statusClass = this.isConnected ? 'success' : 'error';
            connectionStatusElement.textContent = statusText;
            connectionStatusElement.className = `metric-value ${statusClass}`;
        }

        // Update connection uptime
        const uptimeElement = document.getElementById('connectionUptime');
        if (uptimeElement && this.performanceMetrics.connectionStartTime) {
            const uptime = Math.floor((Date.now() - this.performanceMetrics.connectionStartTime) / 1000);
            const hours = Math.floor(uptime / 3600);
            const minutes = Math.floor((uptime % 3600) / 60);
            const seconds = uptime % 60;

            let uptimeText = '';
            if (hours > 0) {
                uptimeText = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                uptimeText = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
            uptimeElement.textContent = `Uptime: ${uptimeText}`;
        }

        // Update performance latency
        const perfLatencyElement = document.getElementById('performanceLatency');
        if (perfLatencyElement) {
            perfLatencyElement.textContent = `${Math.round(this.performanceMetrics.averageLatency)} ms`;
        }

        // Update update rate (updates per minute)
        const updateRateElement = document.getElementById('updateRate');
        if (updateRateElement && this.performanceMetrics.connectionStartTime) {
            const connectionDurationMinutes = (Date.now() - this.performanceMetrics.connectionStartTime) / (1000 * 60);
            const updatesPerMinute = connectionDurationMinutes > 0 ?
                Math.round(this.performanceMetrics.dataCount / connectionDurationMinutes) : 0;
            updateRateElement.textContent = `${updatesPerMinute}/min`;
        }

        // Update data quality (assume 100% for SSE, could be enhanced with error tracking)
        const dataQualityElement = document.getElementById('dataQuality');
        const dataQualityTopElement = document.getElementById('dataQualityTop');
        const quality = this.isConnected ? 100 : 0;

        if (dataQualityElement) {
            dataQualityElement.textContent = `${quality}%`;
        }
        if (dataQualityTopElement) {
            dataQualityTopElement.textContent = `${quality}%`;
        }

        // Update top bar status indicators
        this.updateTopBarStatus();
    }

    updateTopBarStatus() {
        // Update connection indicator
        const connectionIndicator = document.getElementById('connectionIndicator');
        const connectionText = document.getElementById('connectionText');

        if (connectionIndicator && connectionText) {
            connectionIndicator.className = 'status-indicator ' + (this.isConnected ? 'connected' : 'connecting');
            connectionText.textContent = this.isConnected ? 'Connected' : 'Connecting...';
        }

        // Update data freshness indicator
        const dataFreshness = document.getElementById('dataFreshness');
        if (dataFreshness) {
            dataFreshness.className = 'data-freshness ' + (this.isConnected ? 'fresh' : 'error');
            const statusText = dataFreshness.querySelector('span');
            if (statusText) {
                statusText.textContent = this.isConnected ? 'Live Data' : 'No Data';
            }
        }
    }
    
    updateConnectionStatus(status) {
        const statusIndicators = document.querySelectorAll('.status-indicator');
        const systemStatus = document.querySelector('.system-status span:last-child');
        
        statusIndicators.forEach(indicator => {
            indicator.className = `status-indicator ${status}`;
        });
        
        if (systemStatus) {
            const statusText = {
                'connected': 'Real-time Connected',
                'connecting': 'Connecting...',
                'error': 'Connection Error',
                'disconnected': 'Disconnected'
            };
            systemStatus.textContent = statusText[status] || 'Unknown Status';
        }
        
        // Update data freshness indicators
        const freshnessIndicators = document.querySelectorAll('.data-freshness');
        freshnessIndicators.forEach(indicator => {
            const freshnessClass = status === 'connected' ? 'fresh' : 'error';
            indicator.className = `data-freshness ${freshnessClass}`;
            
            const statusText = indicator.querySelector('span:last-child');
            if (statusText) {
                const freshnessText = {
                    'fresh': 'Real-time',
                    'error': 'Offline'
                };
                statusText.textContent = freshnessText[freshnessClass] || 'Unknown';
            }
        });
    }
    
    attemptReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('Max reconnection attempts reached');
            this.updateConnectionStatus('disconnected');
            if (this.onDisconnectedCallback) {
                this.onDisconnectedCallback();
            }
            return;
        }
        
        this.reconnectAttempts++;
        console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
        this.updateConnectionStatus('connecting');
        
        setTimeout(() => {
            this.connect();
        }, this.reconnectDelay * this.reconnectAttempts); // Exponential backoff
    }
    
    disconnect() {
        if (this.eventSource) {
            this.eventSource.close();
            this.eventSource = null;
        }
        this.isConnected = false;
        this.updateConnectionStatus('disconnected');
        console.log('SSE connection closed');
    }
    
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }
}

// Export for use in other scripts
window.SSEDataClient = SSEDataClient;
