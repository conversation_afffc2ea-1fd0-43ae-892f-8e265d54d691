#include <ModbusRTU.h>
#include <Wire.h>
#include <Adafruit_SSD1306.h>
#include <WiFi.h>
#include <HTTPClient.h>

// OLED display settings
#define SCREEN_WIDTH 128
#define SCREEN_HEIGHT 64
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, -1);

// Modbus slave device settings
#define RX_PIN 16     // Changed to GPIO16
#define TX_PIN 17     // Changed to GPIO17
#define RE_DE_PIN 4   // RS485 RE/DE -> GPIO4
#define SLAVE_ID 1    // Modbus slave ID
#define BAUDRATE 9600 // Modbus baud rate

HardwareSerial ModbusSerial(2); // Using Serial2
ModbusRTU mb;
uint16_t val[100];

// WiFi settings
const char* ssid = "kavan";
const char* password = "kavan@113";

// Server settings - Update this with your correct server path
const char* serverUrl = "http://192.168.28.65/backend/receive_data.php";

// Variables to store electrical parameters
float voltage12, voltage23, voltage31;
float pf1, pf2, pf3;
float ia, ib, ic;
float kw, kva, kvar;
float kva1, kva2, kva3;
float frequency;

float InttoFloat(uint16_t Data0, uint16_t Data1) {
  float x;
  unsigned long *p;
  p = (unsigned long*)&x;
  *p = (unsigned long)Data0 << 16 | Data1;
  return x;
}

void updateOLED() {
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);
  display.setCursor(0, 0);
  display.println("WiFi: " + String(WiFi.status() == WL_CONNECTED ? "Connected" : "Disconnected"));
  float batteryVoltage = analogRead(A0) * 3.3 / 4095 * 2;
  display.print("Batt: ");
  display.print(batteryVoltage, 2);
  display.println(" V");
  display.display();
}

void sendDataToServer() {
  if (WiFi.status() == WL_CONNECTED) {
    HTTPClient http;
    http.begin(serverUrl);
    http.addHeader("Content-Type", "application/json");

    // Create JSON data with electrical parameters
    String jsonData = "{";
    jsonData += "\"voltage_1\":" + String(voltage12) + ",";
    jsonData += "\"voltage_2\":" + String(voltage23) + ",";
    jsonData += "\"voltage_3\":" + String(voltage31) + ",";
    jsonData += "\"current_1\":" + String(ia) + ",";
    jsonData += "\"current_2\":" + String(ib) + ",";
    jsonData += "\"current_3\":" + String(ic) + ",";
    jsonData += "\"pf_1\":" + String(pf1) + ",";
    jsonData += "\"pf_2\":" + String(pf2) + ",";
    jsonData += "\"pf_3\":" + String(pf3) + ",";
    jsonData += "\"kva_1\":" + String(kva1) + ",";
    jsonData += "\"kva_2\":" + String(kva2) + ",";
    jsonData += "\"kva_3\":" + String(kva3) + ",";
    jsonData += "\"total_kva\":" + String(kva) + ",";
    jsonData += "\"total_kw\":" + String(kw) + ",";
    jsonData += "\"total_kvar\":" + String(kvar) + ",";
    jsonData += "\"frequency\":" + String(frequency);
    jsonData += "}";

    Serial.println("Sending data to server...");
    Serial.println("URL: " + String(serverUrl));
    Serial.println("Data: " + jsonData);

    int httpResponseCode = http.POST(jsonData);

    if (httpResponseCode > 0) {
      String response = http.getString();
      Serial.println("Server Response Code: " + String(httpResponseCode));
      Serial.println("Server Response: " + response);
    } else {
      Serial.println("Error sending data. HTTP Code: " + String(httpResponseCode));
      Serial.println("Error: " + http.errorToString(httpResponseCode));
    }

    http.end();
  } else {
    Serial.println("WiFi not connected");
  }
}

void testModbusCommunication() {
  Serial.println("\nTesting Modbus Communication...");
  Serial.println("Reading first 10 registers from slave " + String(SLAVE_ID));

  if (!mb.slave()) {
    bool success = mb.readIreg(SLAVE_ID, 0x0000, val, 10);
    while (mb.slave()) mb.task();

    if (success) {
      Serial.println("Modbus read successful");
      for(int i = 0; i < 10; i++) {
        Serial.print("Register ");
        Serial.print(i);
        Serial.print(": 0x");
        Serial.print(val[i], HEX);
        Serial.print(" (");
        Serial.print(val[i]);
        Serial.println(")");
      }
    } else {
      Serial.println("Modbus read failed");
    }
  } else {
    Serial.println("Modbus bus busy");
  }
}

void setup() {
  Serial.begin(115200);
  Serial.println("Starting setup...");

  Wire.begin(9, 10); // SDA=9, SCL=10 for OLED

  if (!display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) {
    Serial.println("SSD1306 allocation failed");
    for (;;);
  }
  display.clearDisplay();
  display.display();

  // Connect to WiFi
  Serial.println("Connecting to WiFi...");
  WiFi.begin(ssid, password);
  while (WiFi.status() != WL_CONNECTED) {
    delay(500);
    Serial.print(".");
  }
  Serial.println("\nWiFi connected");
  Serial.println("IP address: ");
  Serial.println(WiFi.localIP());

  // Initialize Modbus
  Serial.println("Initializing Modbus...");
  ModbusSerial.begin(BAUDRATE, SERIAL_8N1, RX_PIN, TX_PIN);
  mb.begin(&ModbusSerial, RE_DE_PIN);
  mb.master();

  // Test Modbus communication
  testModbusCommunication();

  Serial.println("Setup complete");
}

void loop() {
  static unsigned long lastModbusTime = 0;
  static unsigned long lastSendTime = 0;
  unsigned long currentTime = millis();

  // Read Modbus every second
  if (currentTime - lastModbusTime >= 1000) {
    lastModbusTime = currentTime;

    if (!mb.slave()) {
      bool success = mb.readIreg(SLAVE_ID, 0x0000, val, 100);
      while (mb.slave()) mb.task();

      if (success) {
        // Print raw Modbus values for debugging
        Serial.println("\nRaw Modbus Values:");
        for(int i = 0; i < 60; i++) {
          Serial.print("Register ");
          Serial.print(i);
          Serial.print(": 0x");
          Serial.print(val[i], HEX);
          Serial.print(" (");
          Serial.print(val[i]);
          Serial.println(")");
        }

        // Read and convert values
        voltage12 = InttoFloat(val[8], val[9]);
        voltage23 = InttoFloat(val[10], val[11]);
        voltage31 = InttoFloat(val[12], val[13]);

        ia = InttoFloat(val[16], val[17]);
        ib = InttoFloat(val[18], val[19]);
        ic = InttoFloat(val[20], val[21]);

        kva1 = InttoFloat(val[36], val[37]);
        kva2 = InttoFloat(val[38], val[39]);
        kva3 = InttoFloat(val[40], val[41]);

        pf1 = InttoFloat(val[42], val[43]);
        pf2 = InttoFloat(val[44], val[45]);
        pf3 = InttoFloat(val[46], val[47]);

        kw = InttoFloat(val[52], val[53]);
        kva = InttoFloat(val[54], val[55]);
        kvar = InttoFloat(val[56], val[57]);

        // Read frequency from register 58-59 (adjust if needed based on your device)
        frequency = InttoFloat(val[58], val[59]);

        // Print values to Serial for debugging
        Serial.println("\nConverted Values:");
        Serial.println("Voltages: " + String(voltage12) + "V, " + String(voltage23) + "V, " + String(voltage31) + "V");
        Serial.println("Currents: " + String(ia) + "A, " + String(ib) + "A, " + String(ic) + "A");
        Serial.println("Power Factors: " + String(pf1) + ", " + String(pf2) + ", " + String(pf3));
        Serial.println("kVA: " + String(kva1) + ", " + String(kva2) + ", " + String(kva3));
        Serial.println("Total Power: " + String(kw) + "kW, " + String(kva) + "kVA, " + String(kvar) + "kVAR");
        Serial.println("Frequency: " + String(frequency) + "Hz");
      } else {
        Serial.println("Modbus read failed");
      }
    }
  }

  // Send data to server every 5 seconds
  if (currentTime - lastSendTime >= 5000) {
    lastSendTime = currentTime;
    sendDataToServer();
    updateOLED();
  }
}