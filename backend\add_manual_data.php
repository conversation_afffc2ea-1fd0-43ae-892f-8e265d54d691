<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Handle preflight OPTIONS request
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Check if the request method is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed. Use POST.']);
    exit();
}

// Get the JSON data from the request body
$json = file_get_contents('php://input');
$data = json_decode($json, true);

// Check if JSON is valid
if ($data === null) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON data']);
    exit();
}

// Validate required fields
$requiredFields = ['timestamp', 'voltage_1', 'voltage_2', 'voltage_3', 'current_1', 'current_2', 'current_3', 
                  'pf_1', 'pf_2', 'pf_3', 'total_kw', 'frequency'];
                  
foreach ($requiredFields as $field) {
    if (!isset($data[$field])) {
        http_response_code(400);
        echo json_encode(['error' => "Missing required field: $field"]);
        exit();
    }
}

// Database configuration
$servername = "localhost";
$username = "root";  // Change this to your database username
$password = "";      // Change this to your database password
$dbname = "esp32_data";  // Change this to your database name

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    http_response_code(500);
    echo json_encode(['error' => 'Connection failed: ' . $conn->connect_error]);
    exit();
}

// Check if the table exists
$tableCheck = $conn->query("SHOW TABLES LIKE 'electrical_data'");
if ($tableCheck->num_rows == 0) {
    // Create the table if it doesn't exist
    $createTableSQL = "CREATE TABLE electrical_data (
        id INT(11) NOT NULL AUTO_INCREMENT,
        voltage_1 FLOAT NOT NULL,
        voltage_2 FLOAT NOT NULL,
        voltage_3 FLOAT NOT NULL,
        current_1 FLOAT NOT NULL,
        current_2 FLOAT NOT NULL,
        current_3 FLOAT NOT NULL,
        pf_1 FLOAT NOT NULL,
        pf_2 FLOAT NOT NULL,
        pf_3 FLOAT NOT NULL,
        kva_1 FLOAT NOT NULL,
        kva_2 FLOAT NOT NULL,
        kva_3 FLOAT NOT NULL,
        total_kva FLOAT NOT NULL,
        total_kw FLOAT NOT NULL,
        total_kvar FLOAT NOT NULL,
        frequency FLOAT NOT NULL,
        timestamp DATETIME NOT NULL,
        PRIMARY KEY (id)
    )";
    
    if (!$conn->query($createTableSQL)) {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to create table: ' . $conn->error]);
        exit();
    }
}

// Prepare the SQL statement
$sql = "INSERT INTO electrical_data (
    voltage_1, voltage_2, voltage_3, 
    current_1, current_2, current_3, 
    pf_1, pf_2, pf_3, 
    kva_1, kva_2, kva_3, 
    total_kva, total_kw, total_kvar, 
    frequency, timestamp
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

$stmt = $conn->prepare($sql);

if (!$stmt) {
    http_response_code(500);
    echo json_encode(['error' => 'Failed to prepare statement: ' . $conn->error]);
    exit();
}

// Bind parameters
$stmt->bind_param(
    "dddddddddddddddds",
    $data['voltage_1'],
    $data['voltage_2'],
    $data['voltage_3'],
    $data['current_1'],
    $data['current_2'],
    $data['current_3'],
    $data['pf_1'],
    $data['pf_2'],
    $data['pf_3'],
    $data['kva_1'],
    $data['kva_2'],
    $data['kva_3'],
    $data['total_kva'],
    $data['total_kw'],
    $data['total_kvar'],
    $data['frequency'],
    $data['timestamp']
);

// Execute the statement
if ($stmt->execute()) {
    $insertId = $conn->insert_id;
    echo json_encode([
        'success' => true,
        'message' => 'Record added successfully',
        'id' => $insertId
    ]);
} else {
    http_response_code(500);
    echo json_encode([
        'error' => 'Failed to insert record: ' . $stmt->error,
        'data' => $data
    ]);
}

// Close the connection
$stmt->close();
$conn->close();
?>
