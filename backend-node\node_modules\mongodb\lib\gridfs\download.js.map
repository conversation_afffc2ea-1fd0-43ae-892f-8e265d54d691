{"version": 3, "file": "download.js", "sourceRoot": "", "sources": ["../../src/gridfs/download.ts"], "names": [], "mappings": ";;;AAAA,mCAAkC;AAIlC,+DAA8D;AAE9D,oCAKkB;AAIlB,wCAAgD;AAgGhD;;;;;GAKG;AACH,MAAa,sBAAuB,SAAQ,iBAAQ;IAUlD;;;;;;OAMG;IACH,YACE,MAA+B,EAC/B,KAA6B,EAC7B,cAA0C,EAC1C,MAAgB,EAChB,OAAuC;QAEvC,KAAK,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAC3B,IAAI,CAAC,CAAC,GAAG;YACP,WAAW,EAAE,CAAC;YACd,WAAW,EAAE,CAAC;YACd,SAAS,EAAE,CAAC;YACZ,MAAM;YACN,QAAQ,EAAE,CAAC;YACX,KAAK;YACL,MAAM;YACN,IAAI,EAAE,KAAK;YACX,WAAW,EAAE,CAAC;YACd,OAAO,EAAE;gBACP,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;gBACN,GAAG,OAAO;aACX;YACD,cAAc;YACd,cAAc,EACZ,OAAO,EAAE,SAAS,IAAI,IAAI;gBACxB,CAAC,CAAC,IAAI,4BAAkB,CAAC,EAAE,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,wBAAwB,EAAE,CAAC,EAAE,CAAC;gBACvF,CAAC,CAAC,SAAS;SAChB,CAAC;IACJ,CAAC;IAED;;;;OAIG;IACM,KAAK;QACZ,IAAI,IAAI,CAAC,SAAS;YAAE,OAAO;QAC3B,WAAW,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;IACxC,CAAC;IAED;;;;;;OAMG;IACH,KAAK,CAAC,KAAK,GAAG,CAAC;QACb,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;QAC7B,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;;;OAMG;IACH,GAAG,CAAC,GAAG,GAAG,CAAC;QACT,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC;QACzB,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAChB,IAAI,CAAC,OAAO,EAAE,CAAC;QACf,MAAM,eAAe,GAAG,IAAI,CAAC,CAAC,CAAC,cAAc,EAAE,yBAAyB,EAAE,CAAC;QAC3E,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,EAAE,SAAS,EAAE,eAAe,EAAE,CAAC,CAAC;IAC7D,CAAC;;AA9FH,wDA+FC;AA3FC;;;GAGG;AACa,2BAAI,GAAG,MAAe,CAAC;AAyFzC,SAAS,kBAAkB,CAAC,MAA8B;IACxD,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAClB,MAAM,IAAI,8BAAsB,CAAC,2DAA2D,CAAC,CAAC;IAChG,CAAC;AACH,CAAC;AAED,SAAS,MAAM,CAAC,MAA8B;IAC5C,IAAI,MAAM,CAAC,SAAS;QAAE,OAAO;IAC7B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM;QAAE,OAAO;IAC7B,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;QAAE,OAAO;IAE3B,MAAM,gBAAgB,GAAG,CAAC,GAAoB,EAAE,EAAE;QAChD,IAAI,MAAM,CAAC,SAAS;YAAE,OAAO;QAE7B,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAElB,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;YACzE,OAAO;QACT,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI;YAAE,OAAO;QAE3B,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;QACjE,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;QACtC,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;QACzE,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;YACtB,OAAO,MAAM,CAAC,OAAO,CACnB,IAAI,6BAAqB,CACvB,qCAAqC,GAAG,CAAC,CAAC,eAAe,SAAS,EAAE,CACrE,CACF,CAAC;QACJ,CAAC;QAED,IAAI,GAAG,CAAC,CAAC,GAAG,SAAS,EAAE,CAAC;YACtB,OAAO,MAAM,CAAC,OAAO,CACnB,IAAI,6BAAqB,CAAC,iCAAiC,GAAG,CAAC,CAAC,eAAe,SAAS,EAAE,CAAC,CAC5F,CAAC;QACJ,CAAC;QAED,IAAI,GAAG,GAAG,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC;QAEjE,IAAI,GAAG,CAAC,UAAU,KAAK,cAAc,EAAE,CAAC;YACtC,IAAI,cAAc,IAAI,CAAC,EAAE,CAAC;gBACxB,OAAO,MAAM,CAAC,OAAO,CACnB,IAAI,6BAAqB,CACvB,iCAAiC,GAAG,CAAC,CAAC,0BAA0B,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,2BAA2B,MAAM,CAAC,CAAC,CAAC,SAAS,QAAQ,CAC1I,CACF,CAAC;YACJ,CAAC;YAED,OAAO,MAAM,CAAC,OAAO,CACnB,IAAI,6BAAqB,CACvB,4CAA4C,GAAG,CAAC,UAAU,eAAe,cAAc,EAAE,CAC1F,CACF,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,CAAC,CAAC,SAAS,IAAI,GAAG,CAAC,UAAU,CAAC;QAErC,IAAI,GAAG,CAAC,UAAU,KAAK,CAAC,EAAE,CAAC;YACzB,OAAO,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC3B,CAAC;QAED,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAI,QAAQ,GAAG,IAAI,CAAC;QAEpB,IAAI,MAAM,CAAC,CAAC,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YACjC,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;YAClC,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC;QAC3B,CAAC;QAED,MAAM,aAAa,GAAG,SAAS,KAAK,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,CAAC,CAAC;QAC7D,MAAM,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,GAAG,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;QACpE,IAAI,aAAa,IAAI,MAAM,CAAC,CAAC,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAClD,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,WAAW,CAAC;QAC5D,CAAC;aAAM,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,IAAI,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;YACzE,QAAQ,GAAG,eAAe,CAAC;QAC7B,CAAC;QAED,IAAI,UAAU,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,EAAE,CAAC;YAC3C,GAAG,GAAG,GAAG,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,EAAE,QAAQ,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC;QAC/D,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACjB,OAAO;IACT,CAAC,CAAC;IAEF,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;QACpD,IAAI,MAAM,CAAC,SAAS;YAAE,OAAO;QAC7B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,IAAI,CAAC,MAA8B;IAC1C,MAAM,cAAc,GAAgB,EAAE,CAAC;IACvC,IAAI,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;QAC5B,cAAc,CAAC,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC;IAC1D,CAAC;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9C,cAAc,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;IAC9C,CAAC;IACD,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAC9C,cAAc,CAAC,IAAI,GAAG,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC;IAC9C,CAAC;IAED,MAAM,gBAAgB,GAAG,CAAC,GAAoB,EAAE,EAAE;QAChD,IAAI,MAAM,CAAC,SAAS;YAAE,OAAO;QAE7B,IAAI,CAAC,GAAG,EAAE,CAAC;YACT,MAAM,UAAU,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG;gBACpC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE;gBAChC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,QAAQ,CAAC;YAC7B,MAAM,MAAM,GAAG,sBAAsB,UAAU,gBAAgB,CAAC;YAChE,kBAAkB;YAClB,MAAM,GAAG,GAAG,IAAI,yBAAiB,CAAC,MAAM,CAAC,CAAC;YAC1C,GAAG,CAAC,IAAI,GAAG,QAAQ,CAAC,CAAC,sDAAsD;YAC3E,OAAO,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;QAC7B,CAAC;QAED,8DAA8D;QAC9D,oBAAoB;QACpB,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YACpB,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAClB,OAAO;QACT,CAAC;QAED,IAAI,MAAM,CAAC,SAAS,EAAE,CAAC;YACrB,4DAA4D;YAC5D,iEAAiE;YACjE,kBAAkB;YAClB,MAAM,CAAC,OAAO,EAAE,CAAC;YACjB,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,iBAAiB,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;QAED,MAAM,MAAM,GAAa,EAAE,QAAQ,EAAE,GAAG,CAAC,GAAG,EAAE,CAAC;QAE/C,sEAAsE;QACtE,8EAA8E;QAC9E,+CAA+C;QAC/C,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,IAAI,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;YACvD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;YAChE,IAAI,IAAI,GAAG,CAAC,EAAE,CAAC;gBACb,MAAM,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;QAED,IAAI,eAAmC,CAAC;QACxC,IAAI,CAAC;YACH,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE,yBAAyB,CAClE,4BAA4B,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE,SAAS,IAAI,CACnE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;QAED,MAAM,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,MAAM;aAC9B,IAAI,CAAC,MAAM,EAAE;YACZ,WAAW,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,CAAC,CAAC,mCAAiB,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS;YACxF,SAAS,EAAE,eAAe;SAC3B,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;QAElB,IAAI,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE,CAAC;YAC5B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;QAC9D,CAAC;QAED,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;QAC7D,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,GAAiB,CAAC;QAElC,IAAI,CAAC;YACH,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,eAAe,CAAC,MAAM,EAAE,GAAG,EAAE,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;QACzF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC/B,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC9C,OAAO;IACT,CAAC,CAAC;IAEF,IAAI,eAAmC,CAAC;IACxC,IAAI,CAAC;QACH,eAAe,GAAG,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE,yBAAyB,CAClE,4BAA4B,MAAM,CAAC,CAAC,CAAC,cAAc,EAAE,SAAS,IAAI,CACnE,CAAC;IACJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,MAAM,CAAC,SAAS;YAAE,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;QAC7C,OAAO;IACT,CAAC;IAED,cAAc,CAAC,SAAS,GAAG,eAAe,CAAC;IAE3C,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,EAAE;QACrF,IAAI,MAAM,CAAC,SAAS;YAAE,OAAO;QAC7B,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;IACxB,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,WAAW,CAAC,MAA8B,EAAE,QAAkB;IACrE,IAAI,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,QAAQ,EAAE,CAAC;IACpB,CAAC;IAED,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;QACnB,IAAI,CAAC,MAAM,CAAC,CAAC;QACb,MAAM,CAAC,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,EAAE;QACvB,QAAQ,EAAE,CAAC;IACb,CAAC,CAAC,CAAC;AACL,CAAC;AAED,SAAS,iBAAiB,CACxB,MAA8B,EAC9B,GAAa,EACb,OAAsC;IAEtC,IAAI,OAAO,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,EAAE,CAAC;QACrC,IAAI,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAC/B,MAAM,IAAI,iCAAyB,CACjC,iBAAiB,OAAO,CAAC,KAAK,mDAAmD,GAAG,CAAC,MAAM,GAAG,CAC/F,CAAC;QACJ,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YACtB,MAAM,IAAI,iCAAyB,CAAC,iBAAiB,OAAO,CAAC,KAAK,wBAAwB,CAAC,CAAC;QAC9F,CAAC;QACD,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,IAAI,OAAO,CAAC,GAAG,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC;YACvD,MAAM,IAAI,iCAAyB,CACjC,iBAAiB,OAAO,CAAC,KAAK,0CAA0C,OAAO,CAAC,GAAG,GAAG,CACvF,CAAC;QACJ,CAAC;QAED,MAAM,CAAC,CAAC,CAAC,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,SAAS,CAAC;QAC/E,MAAM,CAAC,CAAC,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;QAE9D,OAAO,OAAO,CAAC,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC;IAC5C,CAAC;IACD,MAAM,IAAI,iCAAyB,CAAC,8BAA8B,CAAC,CAAC;AACtE,CAAC;AAED,SAAS,eAAe,CACtB,MAA8B,EAC9B,GAAa,EACb,MAA+B,EAC/B,OAAsC;IAEtC,IAAI,OAAO,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;QACnC,IAAI,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC;YAC7B,MAAM,IAAI,iCAAyB,CACjC,eAAe,OAAO,CAAC,GAAG,mDAAmD,GAAG,CAAC,MAAM,GAAG,CAC3F,CAAC;QACJ,CAAC;QACD,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI,IAAI,OAAO,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC;YAC/C,MAAM,IAAI,iCAAyB,CAAC,eAAe,OAAO,CAAC,GAAG,wBAAwB,CAAC,CAAC;QAC1F,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAEpF,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,KAAK,CAAC,CAAC;QAE7D,MAAM,CAAC,CAAC,CAAC,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC;QAE9D,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,GAAG,GAAG,CAAC,SAAS,GAAG,OAAO,CAAC,GAAG,CAAC;IAC9E,CAAC;IACD,MAAM,IAAI,iCAAyB,CAAC,4BAA4B,CAAC,CAAC;AACpE,CAAC"}