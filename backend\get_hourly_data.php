<?php
/**
 * API to get hourly electrical data
 *
 * Parameters:
 * - hours_ago: Hours ago from current time (can be negative for future time)
 * - duration: Duration in hours (default: 1)
 * - samples: Number of data points to return (default: 60)
 *
 * Example usage:
 * - get_hourly_data.php?hours_ago=1&duration=1&samples=60 (data from 2 hours ago to 1 hour ago)
 * - get_hourly_data.php?hours_ago=0&duration=1&samples=60 (data from 1 hour ago to now)
 * - get_hourly_data.php?hours_ago=-1&duration=1&samples=60 (data from now to 1 hour in future)
 */
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

// Set timezone to Indian Standard Time (IST)
date_default_timezone_set('Asia/Kolkata');

// Database configuration
$servername = "localhost";
$username = "root";  // Change this to your database username
$password = "";      // Change this to your database password
$dbname = "esp32_data";  // Change this to your database name

// Create connection
$conn = new mysqli($servername, $username, $password, $dbname);

// Check connection
if ($conn->connect_error) {
    die(json_encode(["error" => "Connection failed: " . $conn->connect_error]));
}

// Get query parameters
$hoursAgo = isset($_GET['hours_ago']) ? intval($_GET['hours_ago']) : 0;
$duration = isset($_GET['duration']) ? intval($_GET['duration']) : 1; // Duration in hours
$sampleCount = isset($_GET['samples']) ? intval($_GET['samples']) : 60; // Number of data points to return

// Calculate start and end times
// For positive hoursAgo (past): endTime = now - hoursAgo, startTime = now - (hoursAgo + duration)
// For negative hoursAgo (future): endTime = now + |hoursAgo|, startTime = now + |hoursAgo| - duration
$endTime = date('Y-m-d H:i:s', strtotime("-$hoursAgo hours"));
$startTime = date('Y-m-d H:i:s', strtotime("-" . ($hoursAgo + $duration) . " hours"));

// Ensure start time is always before end time (swap if needed)
if (strtotime($startTime) > strtotime($endTime)) {
    $temp = $startTime;
    $startTime = $endTime;
    $endTime = $temp;
}

// Validate parameters
// Allow negative values for hoursAgo to support backward time navigation
// Negative hoursAgo means future time (e.g., -1 means 1 hour in the future)

if ($duration <= 0) {
    $duration = 1;
}

if ($sampleCount <= 0 || $sampleCount > 1000) {
    $sampleCount = 60;
}

// Get the total number of records in the time range
$countStmt = $conn->prepare("SELECT COUNT(*) as total FROM electrical_data WHERE timestamp BETWEEN ? AND ?");
$countStmt->bind_param("ss", $startTime, $endTime);
$countStmt->execute();
$countResult = $countStmt->get_result();
$totalCount = $countResult->fetch_assoc()['total'];
$countStmt->close();

// If we have more records than requested samples, we need to sample the data
if ($totalCount > $sampleCount) {
    // Calculate the sampling interval
    $interval = floor($totalCount / $sampleCount);

    // Get the IDs of records at regular intervals
    $query = "SELECT id FROM electrical_data WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp ASC";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $startTime, $endTime);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();

    $ids = [];
    $counter = 0;
    while ($row = $result->fetch_assoc()) {
        if ($counter % $interval == 0 && count($ids) < $sampleCount) {
            $ids[] = $row['id'];
        }
        $counter++;
    }

    // Get the actual data for the sampled IDs
    if (!empty($ids)) {
        $idList = implode(',', $ids);
        $query = "SELECT * FROM electrical_data WHERE id IN ($idList) ORDER BY timestamp ASC";
        $result = $conn->query($query);
    } else {
        // Fallback if no IDs were selected - use prepared statement for security
        $fallbackStmt = $conn->prepare("SELECT * FROM electrical_data WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp ASC LIMIT ?");
        $fallbackStmt->bind_param("ssi", $startTime, $endTime, $sampleCount);
        $fallbackStmt->execute();
        $result = $fallbackStmt->get_result();
        $fallbackStmt->close();
    }
} else {
    // If we have fewer records than requested samples, just get all of them
    $query = "SELECT * FROM electrical_data WHERE timestamp BETWEEN ? AND ? ORDER BY timestamp ASC";
    $stmt = $conn->prepare($query);
    $stmt->bind_param("ss", $startTime, $endTime);
    $stmt->execute();
    $result = $stmt->get_result();
    $stmt->close();
}

// Fetch all rows
$data = [];
while ($row = $result->fetch_assoc()) {
    $data[] = [
        "id" => $row["id"],
        "voltage_1" => floatval($row["voltage_1"]),
        "voltage_2" => floatval($row["voltage_2"]),
        "voltage_3" => floatval($row["voltage_3"]),
        "current_1" => floatval($row["current_1"]),
        "current_2" => floatval($row["current_2"]),
        "current_3" => floatval($row["current_3"]),
        "pf_1" => floatval($row["pf_1"]),
        "pf_2" => floatval($row["pf_2"]),
        "pf_3" => floatval($row["pf_3"]),
        "kva_1" => floatval($row["kva_1"]),
        "kva_2" => floatval($row["kva_2"]),
        "kva_3" => floatval($row["kva_3"]),
        "total_kva" => floatval($row["total_kva"]),
        "total_kw" => floatval($row["total_kw"]),
        "total_kvar" => floatval($row["total_kvar"]),
        "frequency" => floatval($row["frequency"]),
        "timestamp" => $row["timestamp"]
    ];
}

// Return the data as JSON
echo json_encode([
    "data" => $data,
    "total" => intval($totalCount),
    "start_time" => $startTime,
    "end_time" => $endTime,
    "hours_ago" => $hoursAgo,
    "duration" => $duration,
    "samples" => count($data),
    "current_time" => date('Y-m-d H:i:s'), // Add current time for debugging
    "time_info" => [
        "start_timestamp" => strtotime($startTime),
        "end_timestamp" => strtotime($endTime),
        "current_timestamp" => time()
    ]
]);

// Close the connection
$conn->close();
?>
